package httputil

import (
	"fmt"
	"runtime"
	"strings"
	"sync"
)

var (
	userAgent     string
	userAgentOnce sync.Once
)

// UserAgent returns the default User-Agent string for LangChainGo HTTP clients.
// Format: program/version langchaingo/version Go/version (GOOS GOARCH)
// Example: "openai-chat-example/devel langchaingo/v0.1.8 Go/go1.21.0 (darwin arm64)"
func UserAgent() string {
	userAgentOnce.Do(func() {
		parts := []string{}

		// Add Go version and platform
		parts = append(parts, fmt.Sprintf("Go/%s", runtime.Version()))
		parts = append(parts, fmt.Sprintf("(%s %s)", runtime.GOOS, runtime.GOARCH))

		userAgent = strings.Join(parts, " ")
	})
	return userAgent
}
