httprr trace v1
768 2415
POST https://api.openai.com/v1/chat/completions HTTP/1.1
Host: api.openai.com
User-Agent: langchaingo-httprr
Content-Length: 565
Authorization: Bearer test-api-key
Content-Type: application/json

{"model":"gpt-4o-2024-08-06","messages":[{"role":"system","content":"You are a helpful assistant"},{"role":"user","content":"What is the age of <PERSON>, a famous comedy screenwriter and an actor."}],"temperature":0,"tools":[{"type":"function","function":{"name":"search","description":"Search by the web search engine","parameters":{"type":"object","properties":{"search_engine":{"type":"string","enum":["google","duckduckgo","bing"]},"search_query":{"type":"string"}},"required":["search_engine","search_query"],"additionalProperties":false},"strict":true}}]}HTTP/2.0 200 OK
Content-Length: 1690
Access-Control-Expose-Headers: X-Request-ID
Alt-Svc: h3=":443"; ma=86400
Cf-Cache-Status: DYNAMIC
Content-Type: application/json
Date: Tue, 03 Jun 2025 08:47:11 GMT
Openai-Organization: lcgo-tst
Openai-Processing-Ms: 1139
Openai-Version: 2020-10-01
Server: cloudflare
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Envoy-Upstream-Service-Time: 1145
X-Ratelimit-Limit-Requests: 10000
X-Ratelimit-Limit-Tokens: 30000000
X-Ratelimit-Remaining-Requests: 9999
X-Ratelimit-Remaining-Tokens: 29999970
X-Ratelimit-Reset-Requests: 6ms
X-Ratelimit-Reset-Tokens: 0s
X-Request-Id: req_ad71e7539eb6ad29b126ff3d2e3e2e2e

{
  "id": "chatcmpl-BeHaYE8vuTVfJErsunMjXYd9u5lYD",
  "object": "chat.completion",
  "created": 1748940430,
  "model": "gpt-4o-2024-08-06",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": null,
        "tool_calls": [
          {
            "id": "call_2HO5CUYqn453wuAKhgsaT3TM",
            "type": "function",
            "function": {
              "name": "search",
              "arguments": "{\"search_engine\": \"google\", \"search_query\": \"Bob Odenkirk age\"}"
            }
          },
          {
            "id": "call_xQQ0uHKTzGN37F7jWEoEakj5",
            "type": "function",
            "function": {
              "name": "search",
              "arguments": "{\"search_engine\": \"duckduckgo\", \"search_query\": \"Bob Odenkirk age\"}"
            }
          },
          {
            "id": "call_d9FYoCNFF90Yy3h9qxuyO4dk",
            "type": "function",
            "function": {
              "name": "search",
              "arguments": "{\"search_engine\": \"bing\", \"search_query\": \"Bob Odenkirk age\"}"
            }
          }
        ],
        "refusal": null,
        "annotations": []
      },
      "logprobs": null,
      "finish_reason": "tool_calls"
    }
  ],
  "usage": {
    "prompt_tokens": 85,
    "completion_tokens": 87,
    "total_tokens": 172,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 0,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "service_tier": "default",
  "system_fingerprint": "fp_a288987b44"
}
