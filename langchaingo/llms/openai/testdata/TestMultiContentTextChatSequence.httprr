httprr trace v1
410 1688
POST https://api.openai.com/v1/chat/completions HTTP/1.1
Host: api.openai.com
User-Agent: langchaingo-httprr
Content-Length: 207
Authorization: Bearer test-api-key
Content-Type: application/json

{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Name some countries"},{"role":"assistant","content":"Spain and Lesotho"},{"role":"user","content":"Which if these is larger?"}],"temperature":0}HTTP/2.0 200 OK
Content-Length: 966
Access-Control-Expose-Headers: X-Request-ID
Alt-Svc: h3=":443"; ma=86400
Cf-Cache-Status: DYNAMIC
Content-Type: application/json
Date: Tue, 03 Jun 2025 08:46:57 GMT
Openai-Organization: lcgo-tst
Openai-Processing-Ms: 648
Openai-Version: 2020-10-01
Server: cloudflare
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Envoy-Upstream-Service-Time: 651
X-Ratelimit-Limit-Requests: 10000
X-Ratelimit-Limit-Tokens: 50000000
X-Ratelimit-Remaining-Requests: 9999
X-Ratelimit-Remaining-Tokens: 49999980
X-Ratelimit-Reset-Requests: 6ms
X-Ratelimit-Reset-Tokens: 0s
X-Request-Id: req_9e6d6dfdfefd33af51eb98b8a2263465

{
  "id": "chatcmpl-BeHaKLFeH1X3w65EVYs8FbQIMOa8a",
  "object": "chat.completion",
  "created": 1748940416,
  "model": "gpt-3.5-turbo-0125",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Spain is larger in terms of land area compared to Lesotho. Spain is the 52nd largest country in the world, while Lesotho is much smaller, ranking 141st in terms of land area.",
        "refusal": null,
        "annotations": []
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 29,
    "completion_tokens": 44,
    "total_tokens": 73,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 0,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "service_tier": "default",
  "system_fingerprint": null
}
