httprr trace v1
490 2040
POST https://api.openai.com/v1/chat/completions HTTP/1.1
Host: api.openai.com
User-Agent: langchaingo-httprr
Content-Length: 287
Authorization: Bearer test-api-key
Content-Type: application/json

{"model":"gpt-4o","messages":[{"role":"user","content":[{"type":"image_url","image_url":{"url":"https://github.com/yudaprama/kawai-agent/langchaingo/blob/main/docs/static/img/parrot-icon.png?raw=true"}},{"text":"describe this image in detail","type":"text"}]}],"temperature":0,"max_completion_tokens":300}HTTP/2.0 200 OK
Content-Length: 1194
Access-Control-Expose-Headers: X-Request-ID
Alt-Svc: h3=":443"; ma=86400
Cf-Cache-Status: DYNAMIC
Content-Type: application/json
Date: Tue, 03 Jun 2025 08:47:01 GMT
Openai-Organization: lcgo-tst
Openai-Processing-Ms: 3187
Openai-Version: 2020-10-01
Server: cloudflare
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Envoy-Upstream-Service-Time: 2559
X-Ratelimit-Limit-Input-Images: 250000
X-Ratelimit-Limit-Requests: 10000
X-Ratelimit-Limit-Tokens: 30000000
X-Ratelimit-Remaining-Input-Images: 249999
X-Ratelimit-Remaining-Requests: 9999
X-Ratelimit-Remaining-Tokens: 29999224
X-Ratelimit-Reset-Input-Images: 0s
X-Ratelimit-Reset-Requests: 6ms
X-Ratelimit-Reset-Tokens: 1ms
X-Request-Id: req_13becc347574450a90191b7d02625c10

{
  "id": "chatcmpl-BeHaNyKXGB3R2D3Z9qfymg0sfbiNs",
  "object": "chat.completion",
  "created": 1748940419,
  "model": "gpt-4o-2024-08-06",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "The image is an emoji of a parrot. The parrot is depicted in a side view, facing to the left. It has a vibrant green body with a gradient that transitions to yellow and orange on its wings and tail. The head is primarily yellow with a small red patch on the forehead. The parrot has a black beak and dark eyes. The background is black, which makes the bright colors of the parrot stand out.",
        "refusal": null,
        "annotations": []
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 267,
    "completion_tokens": 89,
    "total_tokens": 356,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 0,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "service_tier": "default",
  "system_fingerprint": "fp_a288987b44"
}
