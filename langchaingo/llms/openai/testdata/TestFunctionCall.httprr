httprr trace v1
666 1803
POST https://api.openai.com/v1/chat/completions HTTP/1.1
Host: api.openai.com
User-Agent: langchaingo-httprr
Content-Length: 463
Authorization: Bearer test-api-key
Content-Type: application/json

{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"What is the weather like in Boston?"}],"temperature":0,"tools":[{"type":"function","function":{"name":"getCurrentWeather","description":"Get the current weather in a given location","parameters":{"type":"object","properties":{"location":{"type":"string","description":"The city and state, e.g. San Francisco, CA"},"unit":{"type":"string","enum":["celsius","fahrenheit"]}},"required":["location"]}}}]}HTTP/2.0 200 OK
Content-Length: 1080
Access-Control-Expose-Headers: X-Request-ID
Alt-Svc: h3=":443"; ma=86400
Cf-Cache-Status: DYNAMIC
Content-Type: application/json
Date: Tue, 03 Jun 2025 08:47:04 GMT
Openai-Organization: lcgo-tst
Openai-Processing-Ms: 426
Openai-Version: 2020-10-01
Server: cloudflare
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Envoy-Upstream-Service-Time: 431
X-Ratelimit-Limit-Requests: 10000
X-Ratelimit-Limit-Tokens: 50000000
X-Ratelimit-Remaining-Requests: 9999
X-Ratelimit-Remaining-Tokens: 49999989
X-Ratelimit-Reset-Requests: 6ms
X-Ratelimit-Reset-Tokens: 0s
X-Request-Id: req_272aad925237efb5073c8c32b298da3f

{
  "id": "chatcmpl-BeHaSfTo0agBLKtY2uFADMg2wzwss",
  "object": "chat.completion",
  "created": 1748940424,
  "model": "gpt-3.5-turbo-0125",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": null,
        "tool_calls": [
          {
            "id": "call_qOLyxC2V9NjZEprX26FwmjyC",
            "type": "function",
            "function": {
              "name": "getCurrentWeather",
              "arguments": "{\"location\":\"Boston\"}"
            }
          }
        ],
        "refusal": null,
        "annotations": []
      },
      "logprobs": null,
      "finish_reason": "tool_calls"
    }
  ],
  "usage": {
    "prompt_tokens": 81,
    "completion_tokens": 14,
    "total_tokens": 95,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 0,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "service_tier": "default",
  "system_fingerprint": null
}
