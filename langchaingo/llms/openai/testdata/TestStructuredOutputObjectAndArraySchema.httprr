httprr trace v1
739 1682
POST https://api.openai.com/v1/chat/completions HTTP/1.1
Host: api.openai.com
User-Agent: langchaingo-httprr
Content-Length: 536
Authorization: Bearer test-api-key
Content-Type: application/json

{"model":"gpt-4o-2024-08-06","messages":[{"role":"system","content":"You are a student taking a math exam."},{"role":"user","content":"Solve 2 + 2"}],"temperature":0,"response_format":{"type":"json_schema","json_schema":{"name":"math_schema","strict":true,"schema":{"type":"object","properties":{"final_answer":{"type":"string","additionalProperties":false},"steps":{"type":"array","items":{"type":"string","additionalProperties":false},"additionalProperties":false}},"additionalProperties":false,"required":["final_answer","steps"]}}}}HTTP/2.0 200 OK
Content-Length: 958
Access-Control-Expose-Headers: X-Request-ID
Alt-Svc: h3=":443"; ma=86400
Cf-Cache-Status: DYNAMIC
Content-Type: application/json
Date: Tue, 03 Jun 2025 08:47:09 GMT
Openai-Organization: lcgo-tst
Openai-Processing-Ms: 1261
Openai-Version: 2020-10-01
Server: cloudflare
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Envoy-Upstream-Service-Time: 1267
X-Ratelimit-Limit-Requests: 10000
X-Ratelimit-Limit-Tokens: 30000000
X-Ratelimit-Remaining-Requests: 9999
X-Ratelimit-Remaining-Tokens: 29999984
X-Ratelimit-Reset-Requests: 6ms
X-Ratelimit-Reset-Tokens: 0s
X-Request-Id: req_36db73cff599d3299371f09a0ca7e1bc

{
  "id": "chatcmpl-BeHaVBASpSlWxV7h67Y6qM6uw6muZ",
  "object": "chat.completion",
  "created": 1748940427,
  "model": "gpt-4o-2024-08-06",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "{\"final_answer\":\"4\",\"steps\":[\"Start with the expression 2 + 2.\",\"Add the two numbers together: 2 + 2 = 4.\",\"The result of the addition is 4.\"]}",
        "refusal": null,
        "annotations": []
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 66,
    "completion_tokens": 44,
    "total_tokens": 110,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 0,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "service_tier": "default",
  "system_fingerprint": "fp_a288987b44"
}
