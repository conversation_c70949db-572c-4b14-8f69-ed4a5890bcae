httprr trace v1
414 1545
POST https://api.openai.com/v1/chat/completions HTTP/1.1
Host: api.openai.com
User-Agent: langchaingo-httprr
Content-Length: 211
Authorization: Bearer test-api-key
Content-Type: application/json

{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Return a JSON object with a 'greeting' field that says hello"}],"temperature":0,"max_completion_tokens":50,"response_format":{"type":"json_object"}}HTTP/2.0 200 OK
Content-Length: 823
Access-Control-Expose-Headers: X-Request-ID
Alt-Svc: h3=":443"; ma=86400
Cf-Cache-Status: DYNAMIC
Content-Type: application/json
Date: Tue, 03 Jun 2025 08:46:57 GMT
Openai-Organization: lcgo-tst
Openai-Processing-Ms: 320
Openai-Version: 2020-10-01
Server: cloudflare
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Envoy-Upstream-Service-Time: 325
X-Ratelimit-Limit-Requests: 10000
X-Ratelimit-Limit-Tokens: 50000000
X-Ratelimit-Remaining-Requests: 9999
X-Ratelimit-Remaining-Tokens: 49999982
X-Ratelimit-Reset-Requests: 6ms
X-Ratelimit-Reset-Tokens: 0s
X-Request-Id: req_2b994346c58f216b80a1fa5c863ada8c

{
  "id": "chatcmpl-BeHaKGlfnnX6E178YBJ6tuvmaTINE",
  "object": "chat.completion",
  "created": 1748940416,
  "model": "gpt-3.5-turbo-0125",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "{\n  \"greeting\": \"hello\"\n}",
        "refusal": null,
        "annotations": []
      },
      "logprobs": null,
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 21,
    "completion_tokens": 10,
    "total_tokens": 31,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 0,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "service_tier": "default",
  "system_fingerprint": null
}
