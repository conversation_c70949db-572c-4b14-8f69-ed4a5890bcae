httprr trace v1
579 1647
POST https://api.openai.com/v1/chat/completions HTTP/1.1
Host: api.openai.com
User-Agent: langchaingo-httprr
Content-Length: 376
Authorization: Bearer test-api-key
Content-Type: application/json

{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"What's the weather like in Boston?"}],"temperature":0,"max_completion_tokens":100,"functions":[{"name":"get_weather","description":"Get the weather for a location","parameters":{"properties":{"location":{"description":"The location to get weather for","type":"string"}},"required":["location"],"type":"object"}}]}HTTP/2.0 200 OK
Content-Length: 925
Access-Control-Expose-Headers: X-Request-ID
Alt-Svc: h3=":443"; ma=86400
Cf-Cache-Status: DYNAMIC
Content-Type: application/json
Date: Tue, 03 Jun 2025 08:46:57 GMT
Openai-Organization: lcgo-tst
Openai-Processing-Ms: 368
Openai-Version: 2020-10-01
Server: cloudflare
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Envoy-Upstream-Service-Time: 377
X-Ratelimit-Limit-Requests: 10000
X-Ratelimit-Limit-Tokens: 50000000
X-Ratelimit-Remaining-Requests: 9999
X-Ratelimit-Remaining-Tokens: 49999989
X-Ratelimit-Reset-Requests: 6ms
X-Ratelimit-Reset-Tokens: 0s
X-Request-Id: req_bec4b1b35c6de890c3400a1873a4a980

{
  "id": "chatcmpl-BeHaKhHRSp6YjcFv78iYcJlqTS2sp",
  "object": "chat.completion",
  "created": 1748940416,
  "model": "gpt-3.5-turbo-0125",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": null,
        "function_call": {
          "name": "get_weather",
          "arguments": "{\"location\":\"Boston\"}"
        },
        "refusal": null,
        "annotations": []
      },
      "logprobs": null,
      "finish_reason": "function_call"
    }
  ],
  "usage": {
    "prompt_tokens": 62,
    "completion_tokens": 14,
    "total_tokens": 76,
    "prompt_tokens_details": {
      "cached_tokens": 0,
      "audio_tokens": 0
    },
    "completion_tokens_details": {
      "reasoning_tokens": 0,
      "audio_tokens": 0,
      "accepted_prediction_tokens": 0,
      "rejected_prediction_tokens": 0
    }
  },
  "service_tier": "default",
  "system_fingerprint": null
}
