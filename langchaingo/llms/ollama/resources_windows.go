//go:build windows

package ollama

import (
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

// detectMemory detects system memory information on Windows
func detectMemory(resources *SystemResources) error {
	// Use wmic command as a fallback for Windows
	cmd := exec.Command("wmic", "computersystem", "get", "TotalPhysicalMemory", "/value")
	output, err := cmd.Output()
	if err != nil {
		// Fallback: assume reasonable defaults
		resources.TotalMemoryGB = 8.0
		resources.AvailableMemoryGB = 6.0
		return nil
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "TotalPhysicalMemory=") {
			memStr := strings.TrimPrefix(line, "TotalPhysicalMemory=")
			memStr = strings.TrimSpace(memStr)
			if memBytes, err := strconv.ParseInt(memStr, 10, 64); err == nil {
				resources.TotalMemoryGB = float64(memBytes) / (1024 * 1024 * 1024)
				resources.AvailableMemoryGB = resources.TotalMemoryGB * 0.7 // Assume 70% available
				return nil
			}
		}
	}

	// Final fallback
	resources.TotalMemoryGB = 8.0
	resources.AvailableMemoryGB = 6.0
	return nil
}

// detectDiskSpace detects available disk space on Windows
func detectDiskSpace(resources *SystemResources) error {
	// Get current working directory
	wd, err := os.Getwd()
	if err != nil {
		wd = "C:\\"
	}

	// Use wmic to get disk space for the drive
	drive := wd[:2] // Get drive letter (e.g., "C:")

	// Get total disk space
	cmd := exec.Command("wmic", "logicaldisk", "where", fmt.Sprintf("caption='%s'", drive), "get", "size", "/value")
	output, err := cmd.Output()
	if err != nil {
		// Fallback: assume reasonable defaults
		resources.TotalDiskSpaceGB = 500.0
		resources.AvailableDiskSpaceGB = 100.0
		return nil
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "Size=") {
			sizeStr := strings.TrimPrefix(line, "Size=")
			sizeStr = strings.TrimSpace(sizeStr)
			if sizeBytes, err := strconv.ParseInt(sizeStr, 10, 64); err == nil {
				resources.TotalDiskSpaceGB = float64(sizeBytes) / (1024 * 1024 * 1024)
				break
			}
		}
	}

	// Get available disk space
	cmd = exec.Command("wmic", "logicaldisk", "where", fmt.Sprintf("caption='%s'", drive), "get", "freespace", "/value")
	output, err = cmd.Output()
	if err != nil {
		// Fallback: assume 20% of total is available
		resources.AvailableDiskSpaceGB = resources.TotalDiskSpaceGB * 0.2
		return nil
	}

	lines = strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "FreeSpace=") {
			freeStr := strings.TrimPrefix(line, "FreeSpace=")
			freeStr = strings.TrimSpace(freeStr)
			if freeBytes, err := strconv.ParseInt(freeStr, 10, 64); err == nil {
				resources.AvailableDiskSpaceGB = float64(freeBytes) / (1024 * 1024 * 1024)
				break
			}
		}
	}

	// Final fallback if parsing failed
	if resources.TotalDiskSpaceGB == 0 {
		resources.TotalDiskSpaceGB = 500.0
		resources.AvailableDiskSpaceGB = 100.0
	}

	return nil
}

// detectGPU detects GPU information on Windows (best effort)
func detectGPU(resources *SystemResources) {
	// Try wmic command
	cmd := exec.Command("wmic", "path", "win32_VideoController", "get", "name")
	output, err := cmd.Output()
	if err == nil {
		if len(strings.TrimSpace(string(output))) > 0 {
			resources.HasGPU = true
			resources.GPUInfo = "GPU detected via wmic"
		}
	}
}
