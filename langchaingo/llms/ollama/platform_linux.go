//go:build linux

package ollama

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// isOllamaInstalled checks if Ollama is installed on Linux
func isOllamaInstalled() (bool, error) {
	log.Println("🔍 Checking Ollama installation on Linux...")

	// Check if ollama command is available in PATH
	_, err := exec.LookPath("ollama")
	if err == nil {
		log.Println("✅ Ollama found in PATH")
		return true, nil
	}

	// Check common installation paths for Linux
	commonPaths := []string{
		"/usr/local/bin/ollama",  // System-wide installation
		"/usr/bin/ollama",        // Package manager installation
		"/opt/ollama/bin/ollama", // Optional software directory
		filepath.Join(os.Getenv("HOME"), ".local", "bin", "ollama"),  // User installation
		filepath.Join(os.Getenv("HOME"), ".ollama", "bin", "ollama"), // User installation
	}

	for _, path := range commonPaths {
		if _, err := os.Stat(path); err == nil {
			log.Printf("✅ Found Ollama at: %s", path)
			return true, nil
		}
	}

	log.Println("❌ Ollama not found on Linux")
	return false, nil
}

// startOllamaService starts the Ollama service on Linux
func startOllamaService() error {
	// Try systemctl first (systemd)
	if _, err := exec.LookPath("systemctl"); err == nil {
		log.Println("🐧 Attempting to start Ollama via systemctl...")
		cmd := exec.Command("systemctl", "start", "ollama")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama started successfully via systemctl")
			return nil
		}
		log.Printf("⚠️  systemctl start failed, trying service command...")
	}

	// Try service command (SysV init)
	if _, err := exec.LookPath("service"); err == nil {
		log.Println("🐧 Attempting to start Ollama via service command...")
		cmd := exec.Command("service", "ollama", "start")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama started successfully via service command")
			return nil
		}
		log.Printf("⚠️  service command failed, trying direct command...")
	}

	// Fallback to direct command
	log.Println("🦙 Starting Ollama service directly...")
	cmd := exec.Command("ollama", "serve")
	cmd.Stdout = nil
	cmd.Stderr = nil

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start ollama service: %w", err)
	}

	log.Println("✅ Ollama service started successfully")
	return nil
}

// installOllama installs Ollama using the appropriate method for Linux
func installOllama() error {
	log.Println("🐧 Installing Ollama on Linux...")

	// Use the official Ollama installation script
	cmd := exec.Command("curl", "-fsSL", "https://ollama.ai/install.sh")
	installScript, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to download Ollama install script: %w", err)
	}

	// Execute the installation script
	cmd = exec.Command("sh", "-c", string(installScript))
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to install Ollama: %w\nOutput: %s", err, string(output))
	}

	log.Println("✅ Ollama installed successfully on Linux")
	return nil
}

// getOllamaVersionLinux gets the installed Ollama version on Linux
func getOllamaVersionLinux() (string, error) {
	cmd := exec.Command("ollama", "--version")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get Ollama version: %w", err)
	}

	version := strings.TrimSpace(string(output))
	// Extract version number from output like "ollama version is 0.1.32"
	if strings.Contains(version, "version is") {
		parts := strings.Split(version, "version is")
		if len(parts) > 1 {
			version = strings.TrimSpace(parts[1])
		}
	}

	return version, nil
}

// stopOllamaServiceLinux stops the Ollama service on Linux
func stopOllamaServiceLinux() error {
	// Try systemctl first (systemd)
	if _, err := exec.LookPath("systemctl"); err == nil {
		log.Println("🐧 Attempting to stop Ollama via systemctl...")
		cmd := exec.Command("systemctl", "stop", "ollama")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama stopped successfully via systemctl")
			return nil
		}
		log.Printf("⚠️  systemctl stop failed, trying service command...")
	}

	// Try service command (SysV init)
	if _, err := exec.LookPath("service"); err == nil {
		log.Println("🐧 Attempting to stop Ollama via service command...")
		cmd := exec.Command("service", "ollama", "stop")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama stopped successfully via service command")
			return nil
		}
		log.Printf("⚠️  service command failed, trying process termination...")
	}

	// Fallback to killing the process
	log.Println("🦙 Stopping Ollama process...")
	cmd := exec.Command("pkill", "-f", "ollama serve")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to stop Ollama service: %w", err)
	}

	log.Println("✅ Ollama service stopped successfully")
	return nil
}

// isOllamaServiceRunningLinux checks if Ollama service is running on Linux
func isOllamaServiceRunningLinux() (bool, error) {
	// Check if ollama serve process is running
	cmd := exec.Command("pgrep", "-f", "ollama serve")
	err := cmd.Run()

	if err == nil {
		log.Println("✅ Ollama service is running on Linux")
		return true, nil
	}

	log.Println("⚠️  Ollama service is not running on Linux")
	return false, nil
}

// getOllamaConfigDirLinux returns the Ollama configuration directory on Linux
func getOllamaConfigDirLinux() string {
	// Check XDG_CONFIG_HOME first
	if configHome := os.Getenv("XDG_CONFIG_HOME"); configHome != "" {
		return filepath.Join(configHome, "ollama")
	}

	// Default to ~/.config/ollama
	homeDir := os.Getenv("HOME")
	return filepath.Join(homeDir, ".config", "ollama")
}

// getOllamaModelsDirLinux returns the Ollama models directory on Linux
func getOllamaModelsDirLinux() string {
	// Check XDG_DATA_HOME first
	if dataHome := os.Getenv("XDG_DATA_HOME"); dataHome != "" {
		return filepath.Join(dataHome, "ollama", "models")
	}

	// Default to ~/.local/share/ollama/models
	homeDir := os.Getenv("HOME")
	return filepath.Join(homeDir, ".local", "share", "ollama", "models")
}
