//go:build darwin

package ollama

import (
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"syscall"
)

// detectMemory detects system memory information on macOS
func detectMemory(resources *SystemResources) error {
	// Get total memory
	cmd := exec.Command("sysctl", "-n", "hw.memsize")
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to get total memory: %w", err)
	}

	totalBytes, err := strconv.ParseInt(strings.TrimSpace(string(output)), 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse total memory: %w", err)
	}

	resources.TotalMemoryGB = float64(totalBytes) / (1024 * 1024 * 1024)

	// Get available memory (approximation using vm_stat)
	cmd = exec.Command("vm_stat")
	output, err = cmd.Output()
	if err != nil {
		// Fallback: assume 70% of total memory is available
		resources.AvailableMemoryGB = resources.TotalMemoryGB * 0.7
		return nil
	}

	// Parse vm_stat output for free and inactive pages
	lines := strings.Split(string(output), "\n")
	var freePages, inactivePages int64
	pageSize := int64(4096) // Default page size

	for _, line := range lines {
		if strings.Contains(line, "page size of") {
			parts := strings.Fields(line)
			if len(parts) >= 8 {
				if size, err := strconv.ParseInt(parts[7], 10, 64); err == nil {
					pageSize = size
				}
			}
		} else if strings.Contains(line, "Pages free:") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				if pages, err := strconv.ParseInt(strings.TrimSuffix(parts[2], "."), 10, 64); err == nil {
					freePages = pages
				}
			}
		} else if strings.Contains(line, "Pages inactive:") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				if pages, err := strconv.ParseInt(strings.TrimSuffix(parts[2], "."), 10, 64); err == nil {
					inactivePages = pages
				}
			}
		}
	}

	availableBytes := (freePages + inactivePages) * pageSize
	resources.AvailableMemoryGB = float64(availableBytes) / (1024 * 1024 * 1024)

	return nil
}

// detectDiskSpace detects available disk space on macOS
func detectDiskSpace(resources *SystemResources) error {
	// Get current working directory to check disk space
	wd, err := os.Getwd()
	if err != nil {
		wd = "."
	}

	var stat syscall.Statfs_t
	err = syscall.Statfs(wd, &stat)
	if err != nil {
		return fmt.Errorf("failed to get disk space: %w", err)
	}

	// Calculate disk space in GB
	blockSize := uint64(stat.Bsize)
	resources.TotalDiskSpaceGB = float64(stat.Blocks*blockSize) / (1024 * 1024 * 1024)
	resources.AvailableDiskSpaceGB = float64(stat.Bavail*blockSize) / (1024 * 1024 * 1024)

	return nil
}

// detectGPU detects GPU information on macOS (best effort)
func detectGPU(resources *SystemResources) {
	// Try to get GPU info using system_profiler
	cmd := exec.Command("system_profiler", "SPDisplaysDataType", "-json")
	output, err := cmd.Output()
	if err != nil {
		return
	}

	// Simple check for GPU presence
	if strings.Contains(string(output), "spdisplays_vram") {
		resources.HasGPU = true
		resources.GPUInfo = "GPU detected via system_profiler"

		// Try to extract VRAM info (simplified)
		if strings.Contains(string(output), "spdisplays_vram") {
			// This is a simplified extraction - in practice you'd parse the JSON
			resources.GPUMemoryGB = 8.0 // Default assumption for modern Macs
		}
	}
}
