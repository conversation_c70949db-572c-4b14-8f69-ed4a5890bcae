package ollama

import (
	"context"
	"testing"
	"time"
)

func TestManager_NewManager(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	if manager == nil {
		t.<PERSON>al("Manager should not be nil")
	}

	if manager.GetServerURL() != "http://localhost:11434" {
		t.<PERSON><PERSON><PERSON>("Expected server URL to be http://localhost:11434, got %s", manager.GetServerURL())
	}
}

func TestManager_NewManagerWithEmptyURL(t *testing.T) {
	manager, err := NewManager("")
	if err != nil {
		t.Fatalf("Failed to create manager with empty URL: %v", err)
	}

	if manager.GetServerURL() != "http://localhost:11434" {
		t.Errorf("Expected default server URL to be http://localhost:11434, got %s", manager.GetServerURL())
	}
}

func TestManager_IsInstalled(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	installed, err := manager.IsInstalled()
	if err != nil {
		t.Errorf("IsInstalled failed: %v", err)
		return
	}

	t.Logf("Ollama installed: %v", installed)
}

func TestManager_IsServiceRunning(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	running, err := manager.IsServiceRunning(ctx)
	if err != nil {
		t.Errorf("IsServiceRunning failed: %v", err)
		return
	}

	t.Logf("Ollama service running: %v", running)
}

func TestManager_EnsureOllamaReady(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err = manager.EnsureOllamaReady(ctx)
	if err != nil {
		t.Logf("EnsureOllamaReady failed (this may be expected if Ollama is not installed): %v", err)
	} else {
		t.Log("EnsureOllamaReady succeeded")
	}
}

func TestManager_ListLocalModels(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	models, err := manager.ListLocalModels(ctx)
	if err != nil {
		t.Logf("ListLocalModels failed (this may be expected if Ollama is not running): %v", err)
		return
	}

	t.Logf("Found %d local models", len(models))
	for i, model := range models {
		if i < 3 { // Only log first 3 models to avoid spam
			t.Logf("  - %s (size: %d bytes)", model.Name, model.Size)
		}
	}
}

func TestManager_GetPreferredModel(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Test with a model that likely exists
	preferredModel, err := manager.GetPreferredModel(ctx, "qwen3:0.6b")
	if err != nil {
		t.Logf("GetPreferredModel failed (this may be expected if Ollama is not running or model not available): %v", err)
		return
	}

	t.Logf("Preferred model resolved to: %s", preferredModel)
}

func TestManager_ErrorHandling(t *testing.T) {
	// Test with invalid URL
	manager, err := NewManager("http://invalid-host:99999")
	if err != nil {
		t.Fatalf("Failed to create manager with invalid URL: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// This should fail gracefully
	running, err := manager.IsServiceRunning(ctx)
	if err != nil {
		t.Logf("Expected error for invalid host: %v", err)
	} else if !running {
		t.Log("Service correctly reported as not running for invalid host")
	}

	// Test model listing with invalid host (should fail)
	_, err = manager.ListLocalModels(ctx)
	if err != nil {
		t.Logf("Expected error for model listing with invalid host: %v", err)
	} else {
		t.Error("Expected error for model listing with invalid host, but got none")
	}
}

func TestManager_SmartPullModel(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	// Test SmartPullModel with system resource analysis
	modelName, err := manager.SmartPullModel(ctx, "qwen3:0.6b")
	if err != nil {
		t.Logf("SmartPullModel failed (this may be expected if Ollama is not running): %v", err)
		return
	}

	t.Logf("SmartPullModel recommended and pulled: %s", modelName)
}

func TestSystemResourceDetection(t *testing.T) {
	resources, err := DetectSystemResources()
	if err != nil {
		t.Fatalf("Failed to detect system resources: %v", err)
	}

	t.Logf("System Resources Detected:")
	t.Logf("  CPU Cores: %d", resources.CPUCores)
	t.Logf("  Total Memory: %.1f GB", resources.TotalMemoryGB)
	t.Logf("  Available Memory: %.1f GB", resources.AvailableMemoryGB)
	t.Logf("  Total Disk Space: %.1f GB", resources.TotalDiskSpaceGB)
	t.Logf("  Available Disk Space: %.1f GB", resources.AvailableDiskSpaceGB)
	t.Logf("  Has GPU: %v", resources.HasGPU)
	if resources.HasGPU {
		t.Logf("  GPU Info: %s", resources.GPUInfo)
		t.Logf("  GPU Memory: %.1f GB", resources.GPUMemoryGB)
	}

	// Basic sanity checks
	if resources.CPUCores <= 0 {
		t.Error("CPU cores should be positive")
	}
	if resources.TotalMemoryGB <= 0 {
		t.Error("Total memory should be positive")
	}
	if resources.AvailableMemoryGB < 0 {
		t.Error("Available memory should be non-negative")
	}
	if resources.TotalDiskSpaceGB <= 0 {
		t.Error("Total disk space should be positive")
	}
	if resources.AvailableDiskSpaceGB < 0 {
		t.Error("Available disk space should be non-negative")
	}
}

func TestModelRecommendations(t *testing.T) {
	manager, err := NewManager("http://localhost:11434")
	if err != nil {
		t.Fatalf("Failed to create manager: %v", err)
	}

	// Test with different resource scenarios
	testCases := []struct {
		name      string
		resources *SystemResources
	}{
		{
			name: "Low-end system",
			resources: &SystemResources{
				CPUCores:             2,
				TotalMemoryGB:        4.0,
				AvailableMemoryGB:    2.0,
				AvailableDiskSpaceGB: 10.0,
				HasGPU:               false,
			},
		},
		{
			name: "Mid-range system",
			resources: &SystemResources{
				CPUCores:             8,
				TotalMemoryGB:        16.0,
				AvailableMemoryGB:    12.0,
				AvailableDiskSpaceGB: 100.0,
				HasGPU:               true,
				GPUMemoryGB:          8.0,
			},
		},
		{
			name: "High-end system",
			resources: &SystemResources{
				CPUCores:             16,
				TotalMemoryGB:        64.0,
				AvailableMemoryGB:    48.0,
				AvailableDiskSpaceGB: 500.0,
				HasGPU:               true,
				GPUMemoryGB:          24.0,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			recommendations := manager.getModelRecommendations(tc.resources)

			t.Logf("Recommendations for %s:", tc.name)
			recommendedCount := 0
			for _, rec := range recommendations {
				if rec.Recommended {
					recommendedCount++
					t.Logf("  ✅ %s (%s) - %s", rec.ModelName, rec.ParameterSize, rec.Reason)
				} else {
					t.Logf("  ❌ %s (%s) - %s", rec.ModelName, rec.ParameterSize, rec.Reason)
				}
			}

			if recommendedCount == 0 {
				t.Errorf("No models recommended for %s", tc.name)
			}
		})
	}
}
