//go:build darwin

package ollama

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// isOllamaInstalled checks if Ollama is installed on macOS
func isOllamaInstalled() (bool, error) {
	log.Println("🔍 Checking Ollama installation on macOS...")

	// Check if ollama command is available in PATH
	_, err := exec.LookPath("ollama")
	if err == nil {
		log.Println("✅ Ollama found in PATH")
		return true, nil
	}

	// Check common installation paths for macOS
	commonPaths := []string{
		"/usr/local/bin/ollama",                                      // Homebrew Intel
		"/opt/homebrew/bin/ollama",                                   // Homebrew Apple Silicon
		"/Applications/Ollama.app/Contents/Resources/ollama",         // App bundle
		filepath.Join(os.Getenv("HOME"), ".ollama", "bin", "ollama"), // User installation
	}

	for _, path := range commonPaths {
		if _, err := os.Stat(path); err == nil {
			log.Printf("✅ Found Ollama at: %s", path)
			return true, nil
		}
	}

	log.Println("❌ Ollama not found on macOS")
	return false, nil
}

// startOllamaService starts the Ollama service on macOS
func startOllamaService() error {
	// Try brew services start first (preferred for Homebrew installations)
	if _, err := exec.LookPath("brew"); err == nil {
		log.Println("🍺 Attempting to start Ollama via Homebrew services...")
		cmd := exec.Command("brew", "services", "start", "ollama")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama started successfully via Homebrew services")
			return nil
		}
		log.Printf("⚠️  Homebrew services start failed, trying direct command...")
	}

	// Fallback to direct ollama serve command
	log.Println("🦙 Starting Ollama service directly...")
	cmd := exec.Command("ollama", "serve")
	cmd.Stdout = nil
	cmd.Stderr = nil

	// Start the process in background
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start ollama service: %w", err)
	}

	// Don't wait for the process to complete as it runs as a daemon
	log.Println("✅ Ollama service started successfully")
	return nil
}

// installOllama installs Ollama using the appropriate method for macOS
func installOllama() error {
	// Check if Homebrew is installed
	if _, err := exec.LookPath("brew"); err != nil {
		return fmt.Errorf("homebrew is not installed. Please install Homebrew first or install Ollama manually")
	}

	// Install Ollama using Homebrew
	log.Println("🍺 Installing Ollama via Homebrew...")
	cmd := exec.Command("brew", "install", "ollama")

	// Capture output for better error reporting
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to install Ollama via Homebrew: %w\nOutput: %s", err, string(output))
	}

	log.Println("✅ Ollama installed successfully via Homebrew")
	return nil
}

// getOllamaVersionMacOS gets the installed Ollama version on macOS
func getOllamaVersionMacOS() (string, error) {
	cmd := exec.Command("ollama", "--version")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get Ollama version: %w", err)
	}

	version := strings.TrimSpace(string(output))
	// Extract version number from output like "ollama version is 0.1.32"
	if strings.Contains(version, "version is") {
		parts := strings.Split(version, "version is")
		if len(parts) > 1 {
			version = strings.TrimSpace(parts[1])
		}
	}

	return version, nil
}

// stopOllamaServiceMacOS stops the Ollama service on macOS
func stopOllamaServiceMacOS() error {
	// Try brew services stop first (for Homebrew installations)
	if _, err := exec.LookPath("brew"); err == nil {
		log.Println("🍺 Attempting to stop Ollama via Homebrew services...")
		cmd := exec.Command("brew", "services", "stop", "ollama")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama stopped successfully via Homebrew services")
			return nil
		}
		log.Printf("⚠️  Homebrew services stop failed, trying process termination...")
	}

	// Fallback to killing the process
	log.Println("🦙 Stopping Ollama process...")
	cmd := exec.Command("pkill", "-f", "ollama serve")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to stop Ollama service: %w", err)
	}

	log.Println("✅ Ollama service stopped successfully")
	return nil
}

// isOllamaServiceRunningMacOS checks if Ollama service is running on macOS
func isOllamaServiceRunningMacOS() (bool, error) {
	// Check if ollama serve process is running
	cmd := exec.Command("pgrep", "-f", "ollama serve")
	err := cmd.Run()

	if err == nil {
		log.Println("✅ Ollama service is running on macOS")
		return true, nil
	}

	log.Println("⚠️  Ollama service is not running on macOS")
	return false, nil
}

// getOllamaConfigDirMacOS returns the Ollama configuration directory on macOS
func getOllamaConfigDirMacOS() string {
	// Default Ollama config directory on macOS
	homeDir := os.Getenv("HOME")
	return filepath.Join(homeDir, ".ollama")
}

// getOllamaModelsDirMacOS returns the Ollama models directory on macOS
func getOllamaModelsDirMacOS() string {
	// Default Ollama models directory on macOS
	homeDir := os.Getenv("HOME")
	return filepath.Join(homeDir, ".ollama", "models")
}
