//go:build linux

package ollama

import (
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"syscall"
)

// detectMemory detects system memory information on Linux
func detectMemory(resources *SystemResources) error {
	// Read /proc/meminfo
	data, err := os.ReadFile("/proc/meminfo")
	if err != nil {
		return fmt.Errorf("failed to read /proc/meminfo: %w", err)
	}

	lines := strings.Split(string(data), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "MemTotal:") {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				if kb, err := strconv.ParseInt(parts[1], 10, 64); err == nil {
					resources.TotalMemoryGB = float64(kb) / (1024 * 1024)
				}
			}
		} else if strings.HasPrefix(line, "MemAvailable:") {
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				if kb, err := strconv.ParseInt(parts[1], 10, 64); err == nil {
					resources.AvailableMemoryGB = float64(kb) / (1024 * 1024)
				}
			}
		}
	}

	// Fallback if MemAvailable is not available
	if resources.AvailableMemoryGB == 0 {
		resources.AvailableMemoryGB = resources.TotalMemoryGB * 0.7
	}

	return nil
}

// detectDiskSpace detects available disk space on Linux
func detectDiskSpace(resources *SystemResources) error {
	// Get current working directory to check disk space
	wd, err := os.Getwd()
	if err != nil {
		wd = "."
	}

	var stat syscall.Statfs_t
	err = syscall.Statfs(wd, &stat)
	if err != nil {
		return fmt.Errorf("failed to get disk space: %w", err)
	}

	// Calculate disk space in GB
	blockSize := uint64(stat.Bsize)
	resources.TotalDiskSpaceGB = float64(stat.Blocks*blockSize) / (1024 * 1024 * 1024)
	resources.AvailableDiskSpaceGB = float64(stat.Bavail*blockSize) / (1024 * 1024 * 1024)

	return nil
}

// detectGPU detects GPU information on Linux (best effort)
func detectGPU(resources *SystemResources) {
	// Try nvidia-smi first
	cmd := exec.Command("nvidia-smi", "--query-gpu=memory.total", "--format=csv,noheader,nounits")
	output, err := cmd.Output()
	if err == nil {
		resources.HasGPU = true
		resources.GPUInfo = "NVIDIA GPU detected"

		// Parse memory
		if memStr := strings.TrimSpace(string(output)); memStr != "" {
			if memMB, err := strconv.ParseFloat(memStr, 64); err == nil {
				resources.GPUMemoryGB = memMB / 1024
			}
		}
		return
	}

	// Try lspci for other GPUs
	cmd = exec.Command("lspci")
	output, err = cmd.Output()
	if err == nil {
		outputStr := strings.ToLower(string(output))
		if strings.Contains(outputStr, "vga") || strings.Contains(outputStr, "display") {
			resources.HasGPU = true
			resources.GPUInfo = "GPU detected via lspci"
		}
	}
}
