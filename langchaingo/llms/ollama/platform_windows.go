//go:build windows

package ollama

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

// isOllamaInstalled checks if Ollama is installed on Windows
func isOllamaInstalled() (bool, error) {
	log.Println("🔍 Checking Ollama installation on Windows...")

	// Check if ollama.exe is available in PATH
	_, err := exec.LookPath("ollama.exe")
	if err == nil {
		log.Println("✅ Ollama found in PATH")
		return true, nil
	}

	// Check common installation paths for Windows
	commonPaths := []string{
		filepath.Join(os.Getenv("PROGRAMFILES"), "Ollama", "ollama.exe"),
		filepath.Join(os.Getenv("PROGRAMFILES(X86)"), "Ollama", "ollama.exe"),
		filepath.Join(os.Getenv("LOCALAPPDATA"), "Programs", "Ollama", "ollama.exe"),
		filepath.Join(os.<PERSON>env("APPDATA"), "Ollama", "ollama.exe"),
		filepath.Join(os.Getenv("USERPROFILE"), ".ollama", "bin", "ollama.exe"),
	}

	for _, path := range commonPaths {
		if _, err := os.Stat(path); err == nil {
			log.Printf("✅ Found Ollama at: %s", path)
			return true, nil
		}
	}

	log.Println("❌ Ollama not found on Windows")
	return false, nil
}

// startOllamaService starts the Ollama service on Windows
func startOllamaService() error {
	// Try Windows service management first
	if _, err := exec.LookPath("sc"); err == nil {
		log.Println("🪟 Attempting to start Ollama via Windows service...")
		cmd := exec.Command("sc", "start", "ollama")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama started successfully via Windows service")
			return nil
		}
		log.Printf("⚠️  Windows service start failed, trying direct command...")
	}

	// Fallback to direct ollama serve command
	log.Println("🦙 Starting Ollama service directly...")
	cmd := exec.Command("ollama.exe", "serve")
	cmd.Stdout = nil
	cmd.Stderr = nil

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start ollama service: %w", err)
	}

	log.Println("✅ Ollama service started successfully")
	return nil
}

// installOllama installs Ollama using the appropriate method for Windows
func installOllama() error {
	log.Println("🪟 Installing Ollama on Windows...")

	// Check if winget is available (Windows Package Manager)
	if _, err := exec.LookPath("winget"); err == nil {
		log.Println("📦 Attempting to install Ollama via winget...")
		cmd := exec.Command("winget", "install", "Ollama.Ollama")
		output, err := cmd.CombinedOutput()
		if err == nil {
			log.Println("✅ Ollama installed successfully via winget")
			return nil
		}
		log.Printf("⚠️  winget installation failed: %s", string(output))
	}

	// Check if chocolatey is available
	if _, err := exec.LookPath("choco"); err == nil {
		log.Println("🍫 Attempting to install Ollama via Chocolatey...")
		cmd := exec.Command("choco", "install", "ollama")
		output, err := cmd.CombinedOutput()
		if err == nil {
			log.Println("✅ Ollama installed successfully via Chocolatey")
			return nil
		}
		log.Printf("⚠️  Chocolatey installation failed: %s", string(output))
	}

	// Provide manual installation instructions
	return fmt.Errorf("automatic installation failed. Please download and install Ollama manually from https://ollama.ai/download/windows")
}

// getOllamaVersionWindows gets the installed Ollama version on Windows
func getOllamaVersionWindows() (string, error) {
	cmd := exec.Command("ollama.exe", "--version")
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to get Ollama version: %w", err)
	}

	version := strings.TrimSpace(string(output))
	// Extract version number from output like "ollama version is 0.1.32"
	if strings.Contains(version, "version is") {
		parts := strings.Split(version, "version is")
		if len(parts) > 1 {
			version = strings.TrimSpace(parts[1])
		}
	}

	return version, nil
}

// stopOllamaServiceWindows stops the Ollama service on Windows
func stopOllamaServiceWindows() error {
	// Try Windows service management first
	if _, err := exec.LookPath("sc"); err == nil {
		log.Println("🪟 Attempting to stop Ollama via Windows service...")
		cmd := exec.Command("sc", "stop", "ollama")
		if err := cmd.Run(); err == nil {
			log.Println("✅ Ollama stopped successfully via Windows service")
			return nil
		}
		log.Printf("⚠️  Windows service stop failed, trying process termination...")
	}

	// Fallback to killing the process
	log.Println("🦙 Stopping Ollama process...")
	cmd := exec.Command("taskkill", "/F", "/IM", "ollama.exe")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to stop Ollama service: %w", err)
	}

	log.Println("✅ Ollama service stopped successfully")
	return nil
}

// isOllamaServiceRunningWindows checks if Ollama service is running on Windows
func isOllamaServiceRunningWindows() (bool, error) {
	// Check if ollama.exe process is running
	cmd := exec.Command("tasklist", "/FI", "IMAGENAME eq ollama.exe")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("⚠️  Failed to check Ollama process: %v", err)
		return false, nil
	}

	if strings.Contains(string(output), "ollama.exe") {
		log.Println("✅ Ollama service is running on Windows")
		return true, nil
	}

	log.Println("⚠️  Ollama service is not running on Windows")
	return false, nil
}

// getOllamaConfigDirWindows returns the Ollama configuration directory on Windows
func getOllamaConfigDirWindows() string {
	// Use APPDATA for configuration on Windows
	appData := os.Getenv("APPDATA")
	if appData == "" {
		// Fallback to user profile
		userProfile := os.Getenv("USERPROFILE")
		return filepath.Join(userProfile, "AppData", "Roaming", "Ollama")
	}
	return filepath.Join(appData, "Ollama")
}

// getOllamaModelsDirWindows returns the Ollama models directory on Windows
func getOllamaModelsDirWindows() string {
	// Use LOCALAPPDATA for models on Windows
	localAppData := os.Getenv("LOCALAPPDATA")
	if localAppData == "" {
		// Fallback to user profile
		userProfile := os.Getenv("USERPROFILE")
		return filepath.Join(userProfile, "AppData", "Local", "Ollama", "models")
	}
	return filepath.Join(localAppData, "Ollama", "models")
}
