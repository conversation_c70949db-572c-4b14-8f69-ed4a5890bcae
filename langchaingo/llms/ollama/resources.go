package ollama

import (
	"fmt"
	"runtime"
)

// SystemResources represents the system's hardware capabilities
type SystemResources struct {
	CPUCores             int     `json:"cpu_cores"`
	TotalMemoryGB        float64 `json:"total_memory_gb"`
	AvailableMemoryGB    float64 `json:"available_memory_gb"`
	TotalDiskSpaceGB     float64 `json:"total_disk_space_gb"`
	AvailableDiskSpaceGB float64 `json:"available_disk_space_gb"`
	HasGPU               bool    `json:"has_gpu"`
	GPUMemoryGB          float64 `json:"gpu_memory_gb"`
	GPUInfo              string  `json:"gpu_info"`
}

// ModelRecommendation represents a model recommendation based on system resources
type ModelRecommendation struct {
	ModelName       string  `json:"model_name"`
	ParameterSize   string  `json:"parameter_size"`
	EstimatedSizeGB float64 `json:"estimated_size_gb"`
	Recommended     bool    `json:"recommended"`
	Reason          string  `json:"reason"`
	Priority        int     `json:"priority"` // 1 = highest priority
}

// DetectSystemResources analyzes the current system's hardware capabilities
func DetectSystemResources() (*SystemResources, error) {
	resources := &SystemResources{
		CPUCores: runtime.NumCPU(),
	}

	// Detect memory
	if err := detectMemory(resources); err != nil {
		return nil, fmt.Errorf("failed to detect memory: %w", err)
	}

	// Detect disk space
	if err := detectDiskSpace(resources); err != nil {
		return nil, fmt.Errorf("failed to detect disk space: %w", err)
	}

	// Detect GPU (best effort, don't fail if GPU detection fails)
	detectGPU(resources)

	return resources, nil
}

// Platform-specific detection functions are implemented in:
// - resources_darwin.go (macOS) - detectMemory, detectDiskSpace, detectGPU
// - resources_linux.go (Linux) - detectMemory, detectDiskSpace, detectGPU
// - resources_windows.go (Windows) - detectMemory, detectDiskSpace, detectGPU

// All platform-specific functions have been moved to platform-specific files using build tags
