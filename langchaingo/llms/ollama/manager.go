package ollama

import (
	"context"
	"fmt"
	"log"
	"net/url"
	"strings"

	"github.com/yudaprama/kawai-agent/langchaingo/llms/ollama/ollamaclient"
)

// OllamaContainerManager interface defines the Docker operations needed for Ollama container fallback
// This interface is specifically for running Ollama in Docker containers when native installation isn't available
type OllamaContainerManager interface {
	IsInstalled(ctx context.Context) (bool, error)
	IsServiceRunning(ctx context.Context) (bool, error)
	RunOllamaContainer(ctx context.Context) (*OllamaContainerInfo, error)
	IsOllamaContainerRunning(ctx context.Context) (*OllamaContainerInfo, error)
	StopOllamaContainer(ctx context.Context) error
}

// OllamaContainerInfo represents information about a running Ollama container
// This matches the docker.OllamaContainerInfo type for compatibility
type OllamaContainerInfo struct {
	ContainerID string `json:"container_id"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	Port        string `json:"port"`
	URL         string `json:"url"`
}

// Manager provides cross-platform Ollama installation and management capabilities
// Supports both native installation and Docker container fallback
type Manager struct {
	serverURL        string
	client           *ollamaclient.Client
	containerManager OllamaContainerManager // Interface for Ollama container operations
	isContainerMode  bool                   // True if running Ollama in Docker container
}

// NewManager creates a new Ollama manager instance
func NewManager(serverURL string) (*Manager, error) {
	return NewManagerWithContainerFallback(serverURL, nil)
}

// NewManagerWithContainerFallback creates a new Ollama manager instance with container fallback support
func NewManagerWithContainerFallback(serverURL string, containerManager OllamaContainerManager) (*Manager, error) {
	if serverURL == "" {
		serverURL = "http://localhost:11434"
	}

	// Parse the server URL
	parsedURL, err := url.Parse(serverURL)
	if err != nil {
		return nil, fmt.Errorf("invalid server URL: %w", err)
	}

	// Create the ollama client
	client, err := ollamaclient.NewClient(parsedURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create ollama client: %w", err)
	}

	return &Manager{
		serverURL:        serverURL,
		client:           client,
		containerManager: containerManager,
		isContainerMode:  false, // Start in native mode
	}, nil
}

// EnsureOllamaReady ensures Ollama is installed and running
// This is the main entry point for the autoDetectModels integration
// Implements fallback to Docker container when native installation fails
func (m *Manager) EnsureOllamaReady(ctx context.Context) error {
	log.Println("🔍 Checking Ollama installation and service status...")

	// Step 1: Try native Ollama installation
	nativeSuccess := m.tryNativeOllama(ctx)
	if nativeSuccess {
		log.Println("✅ Ollama is ready (native mode)")
		return nil
	}

	// Step 2: If native failed and container manager is available, try container fallback
	if m.containerManager != nil {
		log.Println("🐳 Native Ollama failed, attempting Docker container fallback...")
		containerSuccess := m.tryContainerFallback(ctx)
		if containerSuccess {
			log.Println("✅ Ollama is ready (container mode)")
			return nil
		}
	}

	// Step 3: Both native and container failed
	return fmt.Errorf("failed to ensure Ollama is ready: both native installation and Docker container fallback failed")
}

// tryNativeOllama attempts to set up Ollama natively
func (m *Manager) tryNativeOllama(ctx context.Context) bool {
	// Check if Ollama is installed
	installed, err := m.IsInstalled()
	if err != nil {
		log.Printf("⚠️  Failed to check Ollama installation: %v", err)
		return false
	}

	if !installed {
		log.Println("⚠️  Ollama not found on system")

		// Attempt cross-platform installation
		log.Println("🔧 Attempting to install Ollama...")
		if err := m.InstallOllama(); err != nil {
			log.Printf("⚠️  Failed to install Ollama: %v", err)
			return false
		}
		log.Println("✅ Ollama installed successfully")
	} else {
		log.Println("✅ Ollama is installed")
	}

	// Check if Ollama service is running
	running, err := m.IsServiceRunning(ctx)
	if err != nil {
		log.Printf("⚠️  Failed to check Ollama service status: %v", err)
		return false
	}

	if !running {
		log.Println("⚠️  Ollama service is not running")
		log.Println("🚀 Attempting to start Ollama service...")

		if err := m.StartService(); err != nil {
			log.Printf("⚠️  Failed to start Ollama service: %v", err)
			return false
		}

		log.Println("✅ Ollama service started successfully")
	} else {
		log.Println("✅ Ollama service is running")
	}

	m.isContainerMode = false
	return true
}

// tryContainerFallback attempts to run Ollama in a Docker container
func (m *Manager) tryContainerFallback(ctx context.Context) bool {
	// Check if Docker is available
	dockerInstalled, err := m.containerManager.IsInstalled(ctx)
	if err != nil || !dockerInstalled {
		log.Printf("⚠️  Docker not available for fallback: %v", err)
		return false
	}

	dockerRunning, err := m.containerManager.IsServiceRunning(ctx)
	if err != nil || !dockerRunning {
		log.Printf("⚠️  Docker daemon not running for fallback: %v", err)
		return false
	}

	// Check if Ollama container is already running
	containerInfo, err := m.containerManager.IsOllamaContainerRunning(ctx)
	if err != nil {
		log.Printf("⚠️  Failed to check Ollama container status: %v", err)
		return false
	}

	if containerInfo != nil {
		log.Printf("✅ Found running Ollama container: %s", containerInfo.Name)
		m.isContainerMode = true
		m.serverURL = containerInfo.URL
		return true
	}

	// Run new Ollama container
	containerInfo, err = m.containerManager.RunOllamaContainer(ctx)
	if err != nil {
		log.Printf("⚠️  Failed to run Ollama container: %v", err)
		return false
	}

	log.Printf("✅ Ollama container started: %s", containerInfo.Name)
	m.isContainerMode = true
	m.serverURL = containerInfo.URL
	return true
}

// IsContainerMode returns true if Ollama is running in Docker container mode
func (m *Manager) IsContainerMode() bool {
	return m.isContainerMode
}

// GetMode returns a string describing the current Ollama mode
func (m *Manager) GetMode() string {
	if m.isContainerMode {
		return "container"
	}
	return "native"
}

// GetServerURL returns the current server URL (may be container URL in container mode)
func (m *Manager) GetServerURL() string {
	return m.serverURL
}

// IsInstalled checks if Ollama is installed and functional (service accessible)
// Returns true only if Ollama service is running and accessible
func (m *Manager) IsInstalled() (bool, error) {
	log.Println("🔍 Checking Ollama installation and functionality...")

	// Check if Ollama service is functional (running and accessible)
	ctx := context.Background()
	if functional, err := m.IsOllamaFunctional(ctx); err == nil && functional {
		log.Println("✅ Ollama is installed and functional")
		return true, nil
	}

	// If service is not functional, check if Ollama runtime is installed
	// but return false since the service is not accessible
	if runtimeInstalled, err := m.IsOllamaRuntimeInstalled(); err == nil && runtimeInstalled {
		log.Println("ℹ️  Ollama is installed but service is not running")
		log.Println("⚠️  Ollama runtime found but service is not accessible - Ollama needs to be started")
		return false, nil // Return false because Ollama is not functional
	}

	log.Println("❌ No Ollama installation found")
	return false, nil
}

// IsOllamaRuntimeInstalled checks if Ollama runtime is installed (regardless of running status)
// This is useful for dependency setup to know if Ollama can be started
func (m *Manager) IsOllamaRuntimeInstalled() (bool, error) {
	log.Println("🔍 Checking if Ollama runtime is installed...")

	installed, err := isOllamaInstalled()
	if err != nil {
		return false, err
	}

	if installed {
		log.Println("✅ Ollama runtime is installed")
	} else {
		log.Println("❌ No Ollama runtime found")
	}

	return installed, nil
}

// IsOllamaFunctional checks if Ollama service is running and accessible
func (m *Manager) IsOllamaFunctional(ctx context.Context) (bool, error) {
	// Check if service is running and API is accessible
	running, err := m.client.IsServiceRunning(ctx)
	if err != nil {
		log.Printf("⚠️  Ollama service not accessible: %v", err)
		return false, nil // Don't return error, just not functional
	}

	if running {
		log.Println("🦙 Ollama service is running and accessible")
		return true, nil
	}

	log.Println("⚠️  Ollama service is not running")
	return false, nil
}

// IsServiceRunning checks if the Ollama service is currently running
// This is the original method for backward compatibility
func (m *Manager) IsServiceRunning(ctx context.Context) (bool, error) {
	return m.client.IsServiceRunning(ctx)
}

// StartService starts the Ollama service/daemon
func (m *Manager) StartService() error {
	return startOllamaService()
}

// InstallOllama installs Ollama using the appropriate method for the current platform
func (m *Manager) InstallOllama() error {
	return installOllama()
}

// ListLocalModels returns a list of locally available models
func (m *Manager) ListLocalModels(ctx context.Context) ([]ollamaclient.ModelInfo, error) {
	return m.client.ListLocalModels(ctx)
}

// ListRunningModels returns a list of currently loaded models
func (m *Manager) ListRunningModels(ctx context.Context) ([]ollamaclient.RunningModelInfo, error) {
	return m.client.ListRunningModels(ctx)
}

// PullModel downloads a model from the Ollama library
func (m *Manager) PullModel(ctx context.Context, modelName string) error {
	log.Printf("📥 Pulling model: %s", modelName)

	pullRequest := &ollamaclient.PullRequest{
		Model:  modelName,
		Stream: false, // Use non-streaming for simplicity
	}

	return m.client.Pull(ctx, pullRequest)
}

// GetPreferredModel returns the preferred model name, checking if it exists locally
func (m *Manager) GetPreferredModel(ctx context.Context, preferredModel string) (string, error) {
	localModels, err := m.ListLocalModels(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to list local models: %w", err)
	}

	// Check if the preferred model is available locally
	for _, model := range localModels {
		if strings.Contains(model.Name, preferredModel) || strings.Contains(model.Name, strings.Split(preferredModel, ":")[0]) {
			log.Printf("🎯 Found preferred model: %s", model.Name)
			return model.Name, nil
		}
	}

	// If preferred model not found, try to pull it
	log.Printf("📥 Preferred model not found locally, attempting to pull: %s", preferredModel)
	err = m.PullModel(ctx, preferredModel)
	if err != nil {
		log.Printf("⚠️  Failed to pull preferred model %s: %v", preferredModel, err)

		// Fallback to any available model
		if len(localModels) > 0 {
			fallbackModel := localModels[0].Name
			log.Printf("🔄 Using fallback model: %s", fallbackModel)
			return fallbackModel, nil
		} else {
			return "", fmt.Errorf("no models available and failed to pull preferred model")
		}
	} else {
		log.Printf("✅ Successfully pulled model: %s", preferredModel)
		return preferredModel, nil
	}
}

// SmartPullModel analyzes system resources and recommends/pulls an appropriate model
func (m *Manager) SmartPullModel(ctx context.Context, preferredModel string) (string, error) {
	log.Println("🔍 Analyzing system resources for intelligent model selection...")

	// Detect system resources
	resources, err := DetectSystemResources()
	if err != nil {
		log.Printf("⚠️  Failed to detect system resources: %v", err)
		// Continue with default recommendations
		resources = &SystemResources{
			CPUCores:             4,
			TotalMemoryGB:        8.0,
			AvailableMemoryGB:    6.0,
			TotalDiskSpaceGB:     100.0,
			AvailableDiskSpaceGB: 50.0,
			HasGPU:               false,
		}
	}

	log.Printf("💻 System Resources Detected:")
	log.Printf("   CPU Cores: %d", resources.CPUCores)
	log.Printf("   Total Memory: %.1f GB", resources.TotalMemoryGB)
	log.Printf("   Available Memory: %.1f GB", resources.AvailableMemoryGB)
	log.Printf("   Available Disk Space: %.1f GB", resources.AvailableDiskSpaceGB)
	if resources.HasGPU {
		log.Printf("   GPU: %s (%.1f GB VRAM)", resources.GPUInfo, resources.GPUMemoryGB)
	} else {
		log.Printf("   GPU: Not detected")
	}

	// Get model recommendations based on system resources
	recommendations := m.getModelRecommendations(resources)

	// If a preferred model is specified, check if it's viable
	if preferredModel != "" {
		for _, rec := range recommendations {
			if strings.Contains(rec.ModelName, preferredModel) || strings.Contains(preferredModel, rec.ModelName) {
				if rec.Recommended {
					log.Printf("🎯 Preferred model '%s' is compatible with system resources", preferredModel)
					return m.pullModelWithValidation(ctx, preferredModel, resources)
				} else {
					log.Printf("⚠️  Preferred model '%s' may not be optimal: %s", preferredModel, rec.Reason)
				}
				break
			}
		}
	}

	// Find the best recommended model
	var bestModel *ModelRecommendation
	for _, rec := range recommendations {
		if rec.Recommended && (bestModel == nil || rec.Priority < bestModel.Priority) {
			bestModel = &rec
		}
	}

	if bestModel == nil {
		return "", fmt.Errorf("no suitable models found for current system resources")
	}

	log.Printf("🚀 Recommended model: %s (%s) - %s", bestModel.ModelName, bestModel.ParameterSize, bestModel.Reason)
	return m.pullModelWithValidation(ctx, bestModel.ModelName, resources)
}

// getModelRecommendations returns a list of model recommendations based on system resources
func (m *Manager) getModelRecommendations(resources *SystemResources) []ModelRecommendation {
	recommendations := []ModelRecommendation{
		// Small models (0.5B-1B parameters)
		{
			ModelName:       "qwen3:0.6b",
			ParameterSize:   "0.6B",
			EstimatedSizeGB: 0.5,
			Priority:        1,
		},
		{
			ModelName:       "llama3.2:1b",
			ParameterSize:   "1B",
			EstimatedSizeGB: 0.7,
			Priority:        2,
		},
		// Medium models (3B-8B parameters)
		{
			ModelName:       "llama3.2:3b",
			ParameterSize:   "3B",
			EstimatedSizeGB: 2.0,
			Priority:        3,
		},
		{
			ModelName:       "qwen3:8b",
			ParameterSize:   "8B",
			EstimatedSizeGB: 5.0,
			Priority:        4,
		},
		{
			ModelName:       "llama3.1:8b",
			ParameterSize:   "8B",
			EstimatedSizeGB: 4.7,
			Priority:        5,
		},
		// Large models (13B+ parameters)
		{
			ModelName:       "llama3.1:13b",
			ParameterSize:   "13B",
			EstimatedSizeGB: 7.3,
			Priority:        6,
		},
		{
			ModelName:       "llama3.1:70b",
			ParameterSize:   "70B",
			EstimatedSizeGB: 40.0,
			Priority:        7,
		},
	}

	// Analyze each model against system resources
	for i := range recommendations {
		rec := &recommendations[i]
		rec.Recommended, rec.Reason = m.analyzeModelCompatibility(rec, resources)
	}

	return recommendations
}

// analyzeModelCompatibility determines if a model is compatible with system resources
func (m *Manager) analyzeModelCompatibility(rec *ModelRecommendation, resources *SystemResources) (bool, string) {
	// Check disk space requirement (with 2GB buffer)
	requiredDiskSpace := rec.EstimatedSizeGB + 2.0
	if resources.AvailableDiskSpaceGB < requiredDiskSpace {
		return false, fmt.Sprintf("Insufficient disk space (need %.1f GB, have %.1f GB)",
			requiredDiskSpace, resources.AvailableDiskSpaceGB)
	}

	// Memory requirements (rough estimates)
	var requiredMemoryGB float64
	switch {
	case rec.EstimatedSizeGB <= 1.0: // Small models
		requiredMemoryGB = 2.0
	case rec.EstimatedSizeGB <= 5.0: // Medium models
		requiredMemoryGB = 6.0
	case rec.EstimatedSizeGB <= 10.0: // Large models
		requiredMemoryGB = 12.0
	default: // Very large models
		requiredMemoryGB = 24.0
	}

	if resources.AvailableMemoryGB < requiredMemoryGB {
		return false, fmt.Sprintf("Insufficient memory (need %.1f GB, have %.1f GB)",
			requiredMemoryGB, resources.AvailableMemoryGB)
	}

	// CPU requirements
	var requiredCores int
	switch {
	case rec.EstimatedSizeGB <= 1.0:
		requiredCores = 2
	case rec.EstimatedSizeGB <= 5.0:
		requiredCores = 4
	default:
		requiredCores = 8
	}

	if resources.CPUCores < requiredCores {
		return false, fmt.Sprintf("Insufficient CPU cores (need %d, have %d)",
			requiredCores, resources.CPUCores)
	}

	// Determine recommendation reason
	var reason string
	if resources.HasGPU && resources.GPUMemoryGB >= 4.0 {
		reason = "Excellent performance expected with GPU acceleration"
	} else if resources.AvailableMemoryGB >= requiredMemoryGB*1.5 {
		reason = "Good performance expected with ample memory"
	} else {
		reason = "Adequate performance expected"
	}

	return true, reason
}

// pullModelWithValidation pulls a model with additional safety checks
func (m *Manager) pullModelWithValidation(ctx context.Context, modelName string, resources *SystemResources) (string, error) {
	log.Printf("📥 Pulling model with validation: %s", modelName)

	// Final safety check before pulling
	var estimatedSize float64
	switch {
	case strings.Contains(modelName, "0.6b") || strings.Contains(modelName, "1b"):
		estimatedSize = 1.0
	case strings.Contains(modelName, "3b"):
		estimatedSize = 2.0
	case strings.Contains(modelName, "8b"):
		estimatedSize = 5.0
	case strings.Contains(modelName, "13b"):
		estimatedSize = 8.0
	case strings.Contains(modelName, "70b"):
		estimatedSize = 40.0
	default:
		estimatedSize = 2.0 // Default assumption
	}

	if resources.AvailableDiskSpaceGB < estimatedSize+2.0 {
		return "", fmt.Errorf("insufficient disk space for model %s (need %.1f GB, have %.1f GB)",
			modelName, estimatedSize+2.0, resources.AvailableDiskSpaceGB)
	}

	// Proceed with pulling
	err := m.PullModel(ctx, modelName)
	if err != nil {
		return "", fmt.Errorf("failed to pull model %s: %w", modelName, err)
	}

	log.Printf("✅ Successfully pulled and validated model: %s", modelName)
	return modelName, nil
}
