export type RuntimeStatus =
  | "STATUS$STOPPED"
  | "STATUS$BUILDING_RUNTIME"
  | "STATUS$STARTING_RUNTIME"
  | "STATUS$RUNTIME_STARTED"
  | "STATUS$SETTING_UP_WORKSPACE"
  | "STATUS$SETTING_UP_GIT_HOOKS"
  | "STATUS$READY"
  | "STATUS$ERROR"
  | "STATUS$ERROR_RUNTIME_DISCONNECTED"
  | "STATUS$ERROR_LLM_AUTHENTICATION"
  | "STATUS$ERROR_LLM_SERVICE_UNAVAILABLE"
  | "STATUS$ERROR_LLM_INTERNAL_SERVER_ERROR"
  | "STATUS$ERROR_LLM_OUT_OF_CREDITS"
  | "STATUS$ERROR_LLM_CONTENT_POLICY_VIOLATION"
  | "CHAT_INTERFACE$AGENT_RATE_LIMITED_STOPPED_MESSAGE"
  | "STATUS$GIT_PROVIDER_AUTHENTICATION_ERROR"
  | "STATUS$LLM_RETRY"
  | "STATUS$ERROR_MEMORY";
