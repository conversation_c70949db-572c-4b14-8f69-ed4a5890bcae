VITE_BACKEND_BASE_URL="localhost:3000" # Backend URL without protocol (e.g. localhost:3000)
VITE_BACKEND_HOST="127.0.0.1:3000" # Backend host with port for API connections
VITE_MOCK_API="false" # Enable/disable API mocking with MSW (true or false)
VITE_MOCK_SAAS="false" # Simulate SaaS mode in development (true or false)
VITE_USE_TLS="false" # Use HTTPS/WSS for backend connections (true or false)
VITE_FRONTEND_PORT="3001" # Port to run the frontend application
VITE_INSECURE_SKIP_VERIFY="false" # Skip TLS certificate verification (true or false)
# VITE_GITHUB_TOKEN="" # GitHub token for repository access (used in some tests)
