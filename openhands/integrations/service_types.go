package integrations

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"text/template"

	"kawai/openhands/server"
)

// ProviderType represents the git provider type
type ProviderType string

const (
	// GITHUB represents GitHub provider
	GITHUB ProviderType = "github"
	// GITLAB represents GitLab provider
	GITLAB ProviderType = "gitlab"
	// BITBUCKET represents Bitbucket provider
	BITBUCKET ProviderType = "bitbucket"
)

// TaskType represents the type of suggested task
type TaskType string

const (
	// MERGE_CONFLICTS represents merge conflicts task
	MERGE_CONFLICTS TaskType = "MERGE_CONFLICTS"
	// FAILING_CHECKS represents failing checks task
	FAILING_CHECKS TaskType = "FAILING_CHECKS"
	// UNRESOLVED_COMMENTS represents unresolved comments task
	UNRESOLVED_COMMENTS TaskType = "UNRESOLVED_COMMENTS"
	// OPEN_ISSUE represents open issue task
	OPEN_ISSUE TaskType = "OPEN_ISSUE"
	// OPEN_PR represents open PR task
	OPEN_PR TaskType = "OPEN_PR"
	// CREATE_MICROAGENT represents create microagent task
	CREATE_MICROAGENT TaskType = "CREATE_MICROAGENT"
)

// OwnerType represents the repository owner type
type OwnerType string

const (
	// USER represents user owner type
	USER OwnerType = "user"
	// ORGANIZATION represents organization owner type
	ORGANIZATION OwnerType = "organization"
)

// SuggestedTask represents a suggested task for the user
type SuggestedTask struct {
	GitProvider  ProviderType `json:"git_provider"`
	TaskType     TaskType     `json:"task_type"`
	Repo         string       `json:"repo"`
	IssueNumber  int          `json:"issue_number"`
	Title        string       `json:"title"`
}

// GetProviderTerms returns provider-specific terminology
func (st *SuggestedTask) GetProviderTerms() (map[string]string, error) {
	switch st.GitProvider {
	case GITHUB:
		return map[string]string{
			"requestType":      "Pull Request",
			"requestTypeShort": "PR",
			"apiName":          "GitHub API",
			"tokenEnvVar":      "GITHUB_TOKEN",
			"ciSystem":         "GitHub Actions",
			"ciProvider":       "GitHub",
			"requestVerb":      "pull request",
		}, nil
	case GITLAB:
		return map[string]string{
			"requestType":      "Merge Request",
			"requestTypeShort": "MR",
			"apiName":          "GitLab API",
			"tokenEnvVar":      "GITLAB_TOKEN",
			"ciSystem":         "CI pipelines",
			"ciProvider":       "GitLab",
			"requestVerb":      "merge request",
		}, nil
	case BITBUCKET:
		return map[string]string{
			"requestType":      "Pull Request",
			"requestTypeShort": "PR",
			"apiName":          "Bitbucket API",
			"tokenEnvVar":      "BITBUCKET_TOKEN",
			"ciSystem":         "Bitbucket Pipelines",
			"ciProvider":       "Bitbucket",
			"requestVerb":      "pull request",
		}, nil
	default:
		return nil, fmt.Errorf("provider %s for suggested task prompts", st.GitProvider)
	}
}

// GetPromptForTask returns the prompt template for the task
func (st *SuggestedTask) GetPromptForTask() (string, error) {
	var templateName string
	switch st.TaskType {
	case MERGE_CONFLICTS:
		templateName = "merge_conflict_prompt.j2"
	case FAILING_CHECKS:
		templateName = "failing_checks_prompt.j2"
	case UNRESOLVED_COMMENTS:
		templateName = "unresolved_comments_prompt.j2"
	case OPEN_ISSUE:
		templateName = "open_issue_prompt.j2"
	default:
		return "", fmt.Errorf("unsupported task type: %s", st.TaskType)
	}

	terms, err := st.GetProviderTerms()
	if err != nil {
		return "", err
	}

	// Load template from file system
	tmpl, err := template.ParseFiles("openhands/integrations/templates/suggested_task/" + templateName)
	if err != nil {
		return "", err
	}

	// Prepare template data
	data := map[string]interface{}{
		"issue_number": st.IssueNumber,
		"repo":         st.Repo,
	}
	for k, v := range terms {
		data[k] = v
	}

	// Execute template
	var result strings.Builder
	err = tmpl.Execute(&result, data)
	if err != nil {
		return "", err
	}

	return result.String(), nil
}

// CreateMicroagent represents a microagent creation request
type CreateMicroagent struct {
	Repo        string        `json:"repo"`
	GitProvider *ProviderType `json:"git_provider,omitempty"`
	Title       *string       `json:"title,omitempty"`
}

// User represents a user
type User struct {
	ID        string  `json:"id"`
	Login     string  `json:"login"`
	AvatarURL string  `json:"avatar_url"`
	Company   *string `json:"company,omitempty"`
	Name      *string `json:"name,omitempty"`
	Email     *string `json:"email,omitempty"`
}

// Branch represents a git branch
type Branch struct {
	Name         string  `json:"name"`
	CommitSHA    string  `json:"commit_sha"`
	Protected    bool    `json:"protected"`
	LastPushDate *string `json:"last_push_date,omitempty"` // ISO 8601 format date string
}

// Repository represents a git repository
type Repository struct {
	ID              string      `json:"id"`
	FullName        string      `json:"full_name"`
	GitProvider     ProviderType `json:"git_provider"`
	IsPublic        bool        `json:"is_public"`
	StargazersCount *int        `json:"stargazers_count,omitempty"`
	LinkHeader      *string     `json:"link_header,omitempty"`
	PushedAt        *string     `json:"pushed_at,omitempty"` // ISO 8601 format date string
	OwnerType       *OwnerType  `json:"owner_type,omitempty"`
}

// AuthenticationError is raised when there is an issue with authentication
type AuthenticationError struct {
	Message string
}

func (e AuthenticationError) Error() string {
	return e.Message
}

// UnknownException is raised when there is an issue with communication
type UnknownException struct {
	Message string
}

func (e UnknownException) Error() string {
	return e.Message
}

// RateLimitError is raised when the git provider's API rate limits are exceeded
type RateLimitError struct {
	Message string
}

func (e RateLimitError) Error() string {
	return e.Message
}

// RequestMethod represents HTTP request methods
type RequestMethod string

const (
	// POST represents HTTP POST method
	POST RequestMethod = "post"
	// GET represents HTTP GET method
	GET RequestMethod = "get"
)

// BaseGitService provides base functionality for git services
type BaseGitService struct{}

// Provider returns the provider name (must be implemented by subclasses)
func (b *BaseGitService) Provider() string {
	panic("Subclasses must implement the Provider method")
}

// ExecuteRequest executes an HTTP request
func (b *BaseGitService) ExecuteRequest(ctx context.Context, client *http.Client, url string, headers map[string]string, params interface{}, method RequestMethod) (*http.Response, error) {
	// Implementation would go here - simplified for now
	return nil, fmt.Errorf("not implemented")
}

// HandleHTTPStatusError handles HTTP status errors
func (b *BaseGitService) HandleHTTPStatusError(statusCode int, provider string) error {
	switch statusCode {
	case 401:
		return AuthenticationError{Message: fmt.Sprintf("Invalid %s token", provider)}
	case 429:
		return RateLimitError{Message: "API rate limit exceeded"}
	default:
		return UnknownException{Message: fmt.Sprintf("Unknown error with status code: %d", statusCode)}
	}
}

// HandleHTTPError handles general HTTP errors
func (b *BaseGitService) HandleHTTPError(err error, provider string) error {
	return UnknownException{Message: fmt.Sprintf("HTTP error on %s API: %v", provider, err)}
}

// GitService defines the interface for Git service providers
type GitService interface {
	// GetLatestToken gets latest working token of the user
	GetLatestToken(ctx context.Context) (*string, error)

	// GetUser gets the authenticated user's information
	GetUser(ctx context.Context) (*User, error)

	// SearchRepositories searches for repositories
	SearchRepositories(ctx context.Context, query string, perPage int, sort string, order string) ([]Repository, error)

	// GetRepositories gets repositories for the authenticated user
	GetRepositories(ctx context.Context, sort string, appMode server.AppMode) ([]Repository, error)

	// GetSuggestedTasks gets suggested tasks for the authenticated user across all repositories
	GetSuggestedTasks(ctx context.Context) ([]SuggestedTask, error)

	// GetRepositoryDetailsFromRepoName gets all repository details from repository name
	GetRepositoryDetailsFromRepoName(ctx context.Context, repository string) (*Repository, error)

	// GetBranches gets branches for a repository
	GetBranches(ctx context.Context, repository string) ([]Branch, error)
}
