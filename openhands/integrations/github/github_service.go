package github

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"kawai/openhands/core"
	"kawai/openhands/integrations"
	"kawai/openhands/server"
	"kawai/openhands/utils"
)

// GitHubService is the default implementation of GitService for GitHub integration
type GitHubService struct {
	integrations.BaseGitService
	BaseURL              string
	Token                *string
	Refresh              bool
	UserID               *string
	ExternalTokenManager bool
	ExternalAuthID       *string
	ExternalAuthToken    *string
}

// NewGitHubService creates a new GitHub service instance
func NewGitHubService(userID *string, externalAuthID *string, externalAuthToken *string, token *string, externalTokenManager bool, baseDomain *string) *GitHubService {
	service := &GitHubService{
		BaseURL:              "https://api.github.com",
		Token:                &[]string{""}[0], // Empty string pointer
		Refresh:              false,
		UserID:               userID,
		ExternalTokenManager: externalTokenManager,
		ExternalAuthID:       externalAuthID,
		ExternalAuthToken:    externalAuthToken,
	}

	if token != nil {
		service.Token = token
	}

	if baseDomain != nil && *baseDomain != "github.com" {
		service.BaseURL = fmt.Sprintf("https://%s/api/v3", *baseDomain)
	}

	return service
}

// Provider returns the provider type
func (g *GitHubService) Provider() string {
	return string(integrations.GITHUB)
}

// getGitHubHeaders retrieves the GitHub token and constructs the headers
func (g *GitHubService) getGitHubHeaders(ctx context.Context) (map[string]string, error) {
	if g.Token == nil || *g.Token == "" {
		latestToken, err := g.GetLatestToken(ctx)
		if err != nil {
			return nil, err
		}
		if latestToken != nil {
			g.Token = latestToken
		}
	}

	tokenValue := ""
	if g.Token != nil {
		tokenValue = *g.Token
	}

	return map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", tokenValue),
		"Accept":        "application/vnd.github.v3+json",
	}, nil
}

// hasTokenExpired checks if the token has expired based on status code
func (g *GitHubService) hasTokenExpired(statusCode int) bool {
	return statusCode == 401
}

// GetLatestToken gets the latest token
func (g *GitHubService) GetLatestToken(ctx context.Context) (*string, error) {
	return g.Token, nil
}

// makeRequest makes an HTTP request to the GitHub API
func (g *GitHubService) makeRequest(ctx context.Context, url string, params map[string]interface{}, method integrations.RequestMethod) (map[string]interface{}, map[string]string, error) {
	client := &http.Client{Timeout: 30 * time.Second}

	githubHeaders, err := g.getGitHubHeaders(ctx)
	if err != nil {
		return nil, nil, err
	}

	// Make initial request
	response, err := g.executeRequest(ctx, client, url, githubHeaders, params, method)
	if err != nil {
		return nil, nil, err
	}
	defer response.Body.Close()

	// Handle token refresh if needed
	if g.Refresh && g.hasTokenExpired(response.StatusCode) {
		_, err := g.GetLatestToken(ctx)
		if err != nil {
			return nil, nil, err
		}
		githubHeaders, err = g.getGitHubHeaders(ctx)
		if err != nil {
			return nil, nil, err
		}
		response.Body.Close()
		response, err = g.executeRequest(ctx, client, url, githubHeaders, params, method)
		if err != nil {
			return nil, nil, err
		}
		defer response.Body.Close()
	}

	if response.StatusCode >= 400 {
		return nil, nil, g.HandleHTTPStatusError(response.StatusCode, g.Provider())
	}

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, nil, err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, nil, err
	}

	headers := make(map[string]string)
	if linkHeader := response.Header.Get("Link"); linkHeader != "" {
		headers["Link"] = linkHeader
	}

	return result, headers, nil
}

// executeRequest executes an HTTP request
func (g *GitHubService) executeRequest(ctx context.Context, client *http.Client, url string, headers map[string]string, params map[string]interface{}, method integrations.RequestMethod) (*http.Response, error) {
	var req *http.Request
	var err error

	if method == integrations.POST {
		var body []byte
		if params != nil {
			body, err = json.Marshal(params)
			if err != nil {
				return nil, err
			}
		}
		req, err = http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
		if err != nil {
			return nil, err
		}
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, err = http.NewRequestWithContext(ctx, "GET", url, nil)
		if err != nil {
			return nil, err
		}

		// Add query parameters for GET requests
		if params != nil {
			q := req.URL.Query()
			for k, v := range params {
				q.Add(k, fmt.Sprintf("%v", v))
			}
			req.URL.RawQuery = q.Encode()
		}
	}

	// Set headers
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	return client.Do(req)
}

// GetUser gets the authenticated user's information
func (g *GitHubService) GetUser(ctx context.Context) (*integrations.User, error) {
	url := fmt.Sprintf("%s/user", g.BaseURL)
	response, _, err := g.makeRequest(ctx, url, nil, integrations.GET)
	if err != nil {
		return nil, err
	}

	user := &integrations.User{
		ID:        fmt.Sprintf("%v", response["id"]),
		Login:     fmt.Sprintf("%v", response["login"]),
		AvatarURL: fmt.Sprintf("%v", response["avatar_url"]),
	}

	if company, ok := response["company"]; ok && company != nil {
		companyStr := fmt.Sprintf("%v", company)
		user.Company = &companyStr
	}

	if name, ok := response["name"]; ok && name != nil {
		nameStr := fmt.Sprintf("%v", name)
		user.Name = &nameStr
	}

	if email, ok := response["email"]; ok && email != nil {
		emailStr := fmt.Sprintf("%v", email)
		user.Email = &emailStr
	}

	return user, nil
}

// VerifyAccess verifies if the token is valid by making a simple request
func (g *GitHubService) VerifyAccess(ctx context.Context) error {
	url := g.BaseURL
	_, _, err := g.makeRequest(ctx, url, nil, integrations.GET)
	return err
}

// fetchPaginatedRepos fetches repositories with pagination support
func (g *GitHubService) fetchPaginatedRepos(ctx context.Context, url string, params map[string]interface{}, maxRepos int, extractKey *string) ([]map[string]interface{}, error) {
	var repos []map[string]interface{}
	page := 1

	for len(repos) < maxRepos {
		pageParams := make(map[string]interface{})
		for k, v := range params {
			pageParams[k] = v
		}
		pageParams["page"] = strconv.Itoa(page)

		response, headers, err := g.makeRequest(ctx, url, pageParams, integrations.GET)
		if err != nil {
			return nil, err
		}

		// Extract repositories from response
		var pageRepos []map[string]interface{}
		if extractKey != nil {
			if repoData, ok := response[*extractKey]; ok {
				if repoSlice, ok := repoData.([]interface{}); ok {
					for _, repo := range repoSlice {
						if repoMap, ok := repo.(map[string]interface{}); ok {
							pageRepos = append(pageRepos, repoMap)
						}
					}
				}
			}
		} else {
			// Response might be directly an array of repositories
			// Try to convert the entire response to a slice
			responseBytes, _ := json.Marshal(response)
			var directArray []interface{}
			if json.Unmarshal(responseBytes, &directArray) == nil {
				for _, repo := range directArray {
					if repoMap, ok := repo.(map[string]interface{}); ok {
						pageRepos = append(pageRepos, repoMap)
					}
				}
			}
		}

		if len(pageRepos) == 0 {
			break
		}

		repos = append(repos, pageRepos...)
		page++

		// Check if we've reached the last page
		linkHeader := headers["Link"]
		if !strings.Contains(linkHeader, `rel="next"`) {
			break
		}
	}

	if len(repos) > maxRepos {
		repos = repos[:maxRepos]
	}

	return repos, nil
}

// parsePushedAtDate parses the pushed_at date from repository data
func (g *GitHubService) parsePushedAtDate(repo map[string]interface{}) time.Time {
	if pushedAt, ok := repo["pushed_at"]; ok && pushedAt != nil {
		if pushedAtStr, ok := pushedAt.(string); ok {
			if t, err := time.Parse("2006-01-02T15:04:05Z", pushedAtStr); err == nil {
				return t
			}
		}
	}
	return time.Time{} // Return zero time if parsing fails
}

// GetRepositories gets repositories for the authenticated user
func (g *GitHubService) GetRepositories(ctx context.Context, sortBy string, appMode server.AppMode) ([]integrations.Repository, error) {
	const maxRepos = 1000
	const perPage = 100
	var allRepos []map[string]interface{}

	if appMode == server.SAAS {
		// Get all installation IDs and fetch repos for each one
		installationIDs, err := g.getInstallationIDs(ctx)
		if err != nil {
			return nil, err
		}

		// Iterate through each installation ID
		for _, installationID := range installationIDs {
			params := map[string]interface{}{
				"per_page": strconv.Itoa(perPage),
			}
			url := fmt.Sprintf("%s/user/installations/%d/repositories", g.BaseURL, installationID)

			// Fetch repositories for this installation
			extractKey := "repositories"
			installationRepos, err := g.fetchPaginatedRepos(ctx, url, params, maxRepos-len(allRepos), &extractKey)
			if err != nil {
				return nil, err
			}

			allRepos = append(allRepos, installationRepos...)

			// If we've already reached maxRepos, no need to check other installations
			if len(allRepos) >= maxRepos {
				break
			}
		}

		if sortBy == "pushed" {
			sort.Slice(allRepos, func(i, j int) bool {
				timeI := g.parsePushedAtDate(allRepos[i])
				timeJ := g.parsePushedAtDate(allRepos[j])
				return timeI.After(timeJ) // Reverse order (most recent first)
			})
		}
	} else {
		// Original behavior for non-SaaS mode
		params := map[string]interface{}{
			"per_page": strconv.Itoa(perPage),
			"sort":     sortBy,
		}
		url := fmt.Sprintf("%s/user/repos", g.BaseURL)

		// Fetch user repositories
		var err error
		allRepos, err = g.fetchPaginatedRepos(ctx, url, params, maxRepos, nil)
		if err != nil {
			return nil, err
		}
	}

	// Convert to Repository objects
	var repositories []integrations.Repository
	for _, repo := range allRepos {
		repository := integrations.Repository{
			ID:          fmt.Sprintf("%v", repo["id"]),
			FullName:    fmt.Sprintf("%v", repo["full_name"]),
			GitProvider: integrations.GITHUB,
			IsPublic:    !getBoolValue(repo, "private", true),
		}

		if stargazers, ok := repo["stargazers_count"]; ok && stargazers != nil {
			if count, ok := stargazers.(float64); ok {
				stargazersInt := int(count)
				repository.StargazersCount = &stargazersInt
			}
		}

		// Set owner type
		if owner, ok := repo["owner"].(map[string]interface{}); ok {
			if ownerType, ok := owner["type"].(string); ok {
				if ownerType == "Organization" {
					ownerTypeVal := integrations.ORGANIZATION
					repository.OwnerType = &ownerTypeVal
				} else {
					ownerTypeVal := integrations.USER
					repository.OwnerType = &ownerTypeVal
				}
			}
		}

		repositories = append(repositories, repository)
	}

	return repositories, nil
}

// getBoolValue safely gets a boolean value from a map with a default
func getBoolValue(m map[string]interface{}, key string, defaultValue bool) bool {
	if val, ok := m[key]; ok {
		if boolVal, ok := val.(bool); ok {
			return boolVal
		}
	}
	return defaultValue
}

// getInstallationIDs gets the installation IDs for the authenticated user
func (g *GitHubService) getInstallationIDs(ctx context.Context) ([]int, error) {
	url := fmt.Sprintf("%s/user/installations", g.BaseURL)
	response, _, err := g.makeRequest(ctx, url, nil, integrations.GET)
	if err != nil {
		return nil, err
	}

	var installationIDs []int
	if installations, ok := response["installations"].([]interface{}); ok {
		for _, installation := range installations {
			if installationMap, ok := installation.(map[string]interface{}); ok {
				if id, ok := installationMap["id"].(float64); ok {
					installationIDs = append(installationIDs, int(id))
				}
			}
		}
	}

	return installationIDs, nil
}

// SearchRepositories searches for repositories
func (g *GitHubService) SearchRepositories(ctx context.Context, query string, perPage int, sortBy string, order string) ([]integrations.Repository, error) {
	url := fmt.Sprintf("%s/search/repositories", g.BaseURL)
	// Add is:public to the query to ensure we only search for public repositories
	queryWithVisibility := fmt.Sprintf("%s is:public", query)
	params := map[string]interface{}{
		"q":        queryWithVisibility,
		"per_page": strconv.Itoa(perPage),
		"sort":     sortBy,
		"order":    order,
	}

	response, _, err := g.makeRequest(ctx, url, params, integrations.GET)
	if err != nil {
		return nil, err
	}

	var repositories []integrations.Repository
	if items, ok := response["items"].([]interface{}); ok {
		for _, item := range items {
			if repo, ok := item.(map[string]interface{}); ok {
				repository := integrations.Repository{
					ID:          fmt.Sprintf("%v", repo["id"]),
					FullName:    fmt.Sprintf("%v", repo["full_name"]),
					GitProvider: integrations.GITHUB,
					IsPublic:    true,
				}

				if stargazers, ok := repo["stargazers_count"]; ok && stargazers != nil {
					if count, ok := stargazers.(float64); ok {
						stargazersInt := int(count)
						repository.StargazersCount = &stargazersInt
					}
				}

				// Set owner type
				if owner, ok := repo["owner"].(map[string]interface{}); ok {
					if ownerType, ok := owner["type"].(string); ok {
						if ownerType == "Organization" {
							ownerTypeVal := integrations.ORGANIZATION
							repository.OwnerType = &ownerTypeVal
						} else {
							ownerTypeVal := integrations.USER
							repository.OwnerType = &ownerTypeVal
						}
					}
				}

				repositories = append(repositories, repository)
			}
		}
	}

	return repositories, nil
}

// ExecuteGraphQLQuery executes a GraphQL query against the GitHub API
func (g *GitHubService) ExecuteGraphQLQuery(ctx context.Context, query string, variables map[string]interface{}) (map[string]interface{}, error) {
	client := &http.Client{Timeout: 30 * time.Second}

	githubHeaders, err := g.getGitHubHeaders(ctx)
	if err != nil {
		return nil, err
	}

	payload := map[string]interface{}{
		"query":     query,
		"variables": variables,
	}

	body, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", fmt.Sprintf("%s/graphql", g.BaseURL), bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	for k, v := range githubHeaders {
		req.Header.Set(k, v)
	}

	response, err := client.Do(req)
	if err != nil {
		return nil, g.HandleHTTPError(err, g.Provider())
	}
	defer response.Body.Close()

	if response.StatusCode >= 400 {
		return nil, g.HandleHTTPStatusError(response.StatusCode, g.Provider())
	}

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(responseBody, &result); err != nil {
		return nil, err
	}

	if errors, ok := result["errors"]; ok {
		errorsJSON, _ := json.Marshal(errors)
		return nil, integrations.UnknownException{
			Message: fmt.Sprintf("GraphQL query error: %s", string(errorsJSON)),
		}
	}

	return result, nil
}

// GetSuggestedTasks gets suggested tasks for the authenticated user across all repositories
func (g *GitHubService) GetSuggestedTasks(ctx context.Context) ([]integrations.SuggestedTask, error) {
	// Get user info to use in queries
	user, err := g.GetUser(ctx)
	if err != nil {
		return nil, err
	}

	var tasks []integrations.SuggestedTask
	variables := map[string]interface{}{
		"login": user.Login,
	}

	// Execute PR query
	prResponse, err := g.ExecuteGraphQLQuery(ctx, SuggestedTaskPRGraphQLQuery, variables)
	if err != nil {
		core.OpenHandsLogger.Info("Error fetching suggested task for PRs", map[string]interface{}{
			"error":   err.Error(),
			"signal":  "github_suggested_tasks",
			"user_id": g.ExternalAuthID,
		})
	} else {
		if data, ok := prResponse["data"].(map[string]interface{}); ok {
			if userData, ok := data["user"].(map[string]interface{}); ok {
				if pullRequests, ok := userData["pullRequests"].(map[string]interface{}); ok {
					if nodes, ok := pullRequests["nodes"].([]interface{}); ok {
						for _, node := range nodes {
							if pr, ok := node.(map[string]interface{}); ok {
								repoName := ""
								if repository, ok := pr["repository"].(map[string]interface{}); ok {
									if nameWithOwner, ok := repository["nameWithOwner"].(string); ok {
										repoName = nameWithOwner
									}
								}

								// Start with default task type
								taskType := integrations.OPEN_PR

								// Check for specific states
								if mergeable, ok := pr["mergeable"].(string); ok && mergeable == "CONFLICTING" {
									taskType = integrations.MERGE_CONFLICTS
								} else if commits, ok := pr["commits"].(map[string]interface{}); ok {
									if commitNodes, ok := commits["nodes"].([]interface{}); ok && len(commitNodes) > 0 {
										if commitNode, ok := commitNodes[0].(map[string]interface{}); ok {
											if commit, ok := commitNode["commit"].(map[string]interface{}); ok {
												if statusCheckRollup, ok := commit["statusCheckRollup"].(map[string]interface{}); ok {
													if state, ok := statusCheckRollup["state"].(string); ok && state == "FAILURE" {
														taskType = integrations.FAILING_CHECKS
													}
												}
											}
										}
									}
								}

								// Check for review comments
								if reviews, ok := pr["reviews"].(map[string]interface{}); ok {
									if reviewNodes, ok := reviews["nodes"].([]interface{}); ok {
										for _, reviewNode := range reviewNodes {
											if review, ok := reviewNode.(map[string]interface{}); ok {
												if state, ok := review["state"].(string); ok {
													if state == "CHANGES_REQUESTED" || state == "COMMENTED" {
														taskType = integrations.UNRESOLVED_COMMENTS
														break
													}
												}
											}
										}
									}
								}

								// Only add the task if it's not OPEN_PR
								if taskType != integrations.OPEN_PR {
									issueNumber := 0
									if number, ok := pr["number"].(float64); ok {
										issueNumber = int(number)
									}

									title := ""
									if titleStr, ok := pr["title"].(string); ok {
										title = titleStr
									}

									tasks = append(tasks, integrations.SuggestedTask{
										GitProvider: integrations.GITHUB,
										TaskType:    taskType,
										Repo:        repoName,
										IssueNumber: issueNumber,
										Title:       title,
									})
								}
							}
						}
					}
				}
			}
		}
	}

	// Execute issue query
	issueResponse, err := g.ExecuteGraphQLQuery(ctx, SuggestedTaskIssueGraphQLQuery, variables)
	if err != nil {
		core.OpenHandsLogger.Info("Error fetching suggested task for issues", map[string]interface{}{
			"error":   err.Error(),
			"signal":  "github_suggested_tasks",
			"user_id": g.ExternalAuthID,
		})
		return tasks, nil
	}

	if data, ok := issueResponse["data"].(map[string]interface{}); ok {
		if userData, ok := data["user"].(map[string]interface{}); ok {
			if issues, ok := userData["issues"].(map[string]interface{}); ok {
				if nodes, ok := issues["nodes"].([]interface{}); ok {
					for _, node := range nodes {
						if issue, ok := node.(map[string]interface{}); ok {
							repoName := ""
							if repository, ok := issue["repository"].(map[string]interface{}); ok {
								if nameWithOwner, ok := repository["nameWithOwner"].(string); ok {
									repoName = nameWithOwner
								}
							}

							issueNumber := 0
							if number, ok := issue["number"].(float64); ok {
								issueNumber = int(number)
							}

							title := ""
							if titleStr, ok := issue["title"].(string); ok {
								title = titleStr
							}

							tasks = append(tasks, integrations.SuggestedTask{
								GitProvider: integrations.GITHUB,
								TaskType:    integrations.OPEN_ISSUE,
								Repo:        repoName,
								IssueNumber: issueNumber,
								Title:       title,
							})
						}
					}
				}
			}
		}
	}

	return tasks, nil
}

// GetRepositoryDetailsFromRepoName gets all repository details from repository name
func (g *GitHubService) GetRepositoryDetailsFromRepoName(ctx context.Context, repository string) (*integrations.Repository, error) {
	url := fmt.Sprintf("%s/repos/%s", g.BaseURL, repository)
	repo, _, err := g.makeRequest(ctx, url, nil, integrations.GET)
	if err != nil {
		return nil, err
	}

	result := &integrations.Repository{
		ID:          fmt.Sprintf("%v", repo["id"]),
		FullName:    fmt.Sprintf("%v", repo["full_name"]),
		GitProvider: integrations.GITHUB,
		IsPublic:    !getBoolValue(repo, "private", true),
	}

	if stargazers, ok := repo["stargazers_count"]; ok && stargazers != nil {
		if count, ok := stargazers.(float64); ok {
			stargazersInt := int(count)
			result.StargazersCount = &stargazersInt
		}
	}

	// Set owner type
	if owner, ok := repo["owner"].(map[string]interface{}); ok {
		if ownerType, ok := owner["type"].(string); ok {
			if ownerType == "Organization" {
				ownerTypeVal := integrations.ORGANIZATION
				result.OwnerType = &ownerTypeVal
			} else {
				ownerTypeVal := integrations.USER
				result.OwnerType = &ownerTypeVal
			}
		}
	}

	return result, nil
}

// GetBranches gets branches for a repository
func (g *GitHubService) GetBranches(ctx context.Context, repository string) ([]integrations.Branch, error) {
	url := fmt.Sprintf("%s/repos/%s/branches", g.BaseURL, repository)

	// Set maximum branches to fetch (10 pages with 100 per page)
	const maxBranches = 1000
	const perPage = 100

	var allBranches []integrations.Branch
	page := 1

	// Fetch up to 10 pages of branches
	for page <= 10 && len(allBranches) < maxBranches {
		params := map[string]interface{}{
			"per_page": strconv.Itoa(perPage),
			"page":     strconv.Itoa(page),
		}

		response, headers, err := g.makeRequest(ctx, url, params, integrations.GET)
		if err != nil {
			return nil, err
		}

		// The response should be directly an array of branches
		var branchData []interface{}
		if responseBytes, err := json.Marshal(response); err == nil {
			var directArray []interface{}
			if err := json.Unmarshal(responseBytes, &directArray); err == nil {
				branchData = directArray
			}
		}

		if len(branchData) == 0 {
			break
		}

		for _, branchItem := range branchData {
			if branchMap, ok := branchItem.(map[string]interface{}); ok {
				// Extract the last commit date if available
				var lastPushDate *string
				if commit, ok := branchMap["commit"].(map[string]interface{}); ok {
					if commitInfo, ok := commit["commit"].(map[string]interface{}); ok {
						if committer, ok := commitInfo["committer"].(map[string]interface{}); ok {
							if date, ok := committer["date"].(string); ok {
								lastPushDate = &date
							}
						}
					}
				}

				branch := integrations.Branch{
					Name:         fmt.Sprintf("%v", branchMap["name"]),
					CommitSHA:    "",
					Protected:    getBoolValue(branchMap, "protected", false),
					LastPushDate: lastPushDate,
				}

				if commit, ok := branchMap["commit"].(map[string]interface{}); ok {
					if sha, ok := commit["sha"].(string); ok {
						branch.CommitSHA = sha
					}
				}

				allBranches = append(allBranches, branch)
			}
		}

		page++

		// Check if we've reached the last page
		linkHeader := headers["Link"]
		if !strings.Contains(linkHeader, `rel="next"`) {
			break
		}
	}

	return allBranches, nil
}

// CreatePR creates a PR using user credentials
func (g *GitHubService) CreatePR(ctx context.Context, repoName string, sourceBranch string, targetBranch string, title string, body *string, draft bool, labels []string) (string, error) {
	url := fmt.Sprintf("%s/repos/%s/pulls", g.BaseURL, repoName)

	// Set default body if none provided
	bodyText := fmt.Sprintf("Merging changes from %s into %s", sourceBranch, targetBranch)
	if body != nil {
		bodyText = *body
	}

	// Prepare the request payload
	payload := map[string]interface{}{
		"title": title,
		"head":  sourceBranch,
		"base":  targetBranch,
		"body":  bodyText,
		"draft": draft,
	}

	// Make the POST request to create the PR
	response, _, err := g.makeRequest(ctx, url, payload, integrations.POST)
	if err != nil {
		return "", err
	}

	// Add labels if provided (PRs are a type of issue in GitHub's API)
	if len(labels) > 0 {
		if number, ok := response["number"].(float64); ok {
			prNumber := int(number)
			labelsURL := fmt.Sprintf("%s/repos/%s/issues/%d/labels", g.BaseURL, repoName, prNumber)
			labelsPayload := map[string]interface{}{
				"labels": labels,
			}
			_, _, err := g.makeRequest(ctx, labelsURL, labelsPayload, integrations.POST)
			if err != nil {
				// Log error but don't fail the PR creation
				core.OpenHandsLogger.Warning("Failed to add labels to PR", map[string]interface{}{
					"error": err.Error(),
					"repo":  repoName,
					"pr":    prNumber,
				})
			}
		}
	}

	// Return the HTML URL of the created PR
	if htmlURL, ok := response["html_url"].(string); ok {
		return htmlURL, nil
	}

	return "", fmt.Errorf("failed to get PR URL from response")
}

// GitHubServiceImpl creates a GitHub service implementation
func GitHubServiceImpl() integrations.GitService {
	githubServiceCls := os.Getenv("OPENHANDS_GITHUB_SERVICE_CLS")
	if githubServiceCls == "" {
		githubServiceCls = "openhands.integrations.github.github_service.GitHubService"
	}

	// In a real implementation, this would use the import utils to get the implementation
	// For now, we'll return a new GitHubService instance
	impl, err := utils.GetImpl(&GitHubService{}, &githubServiceCls)
	if err != nil {
		core.OpenHandsLogger.Error("Failed to get GitHub service implementation", map[string]interface{}{
			"error": err.Error(),
			"class": githubServiceCls,
		})
		return NewGitHubService(nil, nil, nil, nil, false, nil)
	}

	if service, ok := impl.(integrations.GitService); ok {
		return service
	}

	return NewGitHubService(nil, nil, nil, nil, false, nil)
}
