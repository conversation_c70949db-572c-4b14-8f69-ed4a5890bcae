package llm

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"kawai/langchaingo/llms"
	"kawai/langchaingo/llms/openai"
	"kawai/openhands/core"
	"kawai/openhands/core/config"
)

// Model constants
var (
	// CachePromptSupportedModels lists models that support prompt caching
	CachePromptSupportedModels = []string{
		"claude-3-7-sonnet-20250219",
		"claude-sonnet-3-7-latest",
		"claude-3.7-sonnet",
		"claude-3-5-sonnet-20241022",
		"claude-3-5-sonnet-20240620",
		"claude-3-5-haiku-20241022",
		"claude-3-haiku-20240307",
		"claude-3-opus-20240229",
		"claude-sonnet-4-20250514",
		"claude-sonnet-4",
		"claude-opus-4-20250514",
	}

	// FunctionCallingSupportedModels lists models that support function calling
	FunctionCallingSupportedModels = []string{
		"claude-3-7-sonnet-20250219",
		"claude-sonnet-3-7-latest",
		"claude-3-5-sonnet",
		"claude-3-5-sonnet-20240620",
		"claude-3-5-sonnet-20241022",
		"claude-3.5-haiku",
		"claude-3-5-haiku-20241022",
		"claude-sonnet-4-20250514",
		"claude-sonnet-4",
		"claude-opus-4-20250514",
		"gpt-4o-mini",
		"gpt-4o",
		"o1-2024-12-17",
		"o3-mini-2025-01-31",
		"o3-mini",
		"o3",
		"o3-2025-04-16",
		"o4-mini",
		"o4-mini-2025-04-16",
		"gemini-2.5-pro",
		"gpt-4.1",
		"kimi-k2-0711-preview",
	}

	// ReasoningEffortSupportedModels lists models that support reasoning effort
	ReasoningEffortSupportedModels = []string{
		"o1-2024-12-17",
		"o1",
		"o3",
		"o3-2025-04-16",
		"o3-mini-2025-01-31",
		"o3-mini",
		"o4-mini",
		"o4-mini-2025-04-16",
		"gemini-2.5-flash",
		"gemini-2.5-pro",
	}

	// ModelsWithoutStopWords lists models that don't support stop words
	ModelsWithoutStopWords = []string{
		"o1-mini",
		"o1-preview",
		"o1",
		"o1-2024-12-17",
		"xai/grok-4-0709",
	}
)

// ModelInfo represents information about a model
type ModelInfo struct {
	MaxInputTokens  *int  `json:"max_input_tokens,omitempty"`
	MaxOutputTokens *int  `json:"max_output_tokens,omitempty"`
	MaxTokens       *int  `json:"max_tokens,omitempty"`
	SupportsVision  *bool `json:"supports_vision,omitempty"`
}

// CallOption is an alias for langchaingo CallOption
type CallOption = llms.CallOption

// ContentResponse is an alias for langchaingo ContentResponse
type ContentResponse = llms.ContentResponse

// ContentChoice is an alias for langchaingo ContentChoice
type ContentChoice = llms.ContentChoice

// LLM represents a Language Model instance
type LLM struct {
	DebugMixin
	RetryMixin

	Config                   *config.LLMConfig
	Metrics                  *Metrics
	RetryListener            RetryListener
	ModelInfo                *ModelInfo
	CostMetricSupported      bool
	FunctionCallingActive    bool
	TriedModelInfo           bool

	// LangChain Go LLM implementation
	llmInstance              llms.Model
}

// NewLLM creates a new LLM instance
func NewLLM(cfg *config.LLMConfig, metrics *Metrics, retryListener RetryListener) (*LLM, error) {
	if cfg == nil {
		cfg = config.NewLLMConfig()
	}

	if metrics == nil {
		metrics = NewMetrics(cfg.Model)
	}

	llm := &LLM{
		Config:              cfg,
		Metrics:             metrics,
		RetryListener:       retryListener,
		CostMetricSupported: true,
		TriedModelInfo:      false,
	}

	// Initialize model info
	if err := llm.initModelInfo(); err != nil {
		core.OpenHandsLogger.Warning("Failed to initialize model info", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// Create log completions folder if needed
	if cfg.LogCompletions {
		if cfg.LogCompletionsFolder == "" {
			return nil, fmt.Errorf("log_completions_folder is required when log_completions is enabled")
		}
		if err := os.MkdirAll(cfg.LogCompletionsFolder, 0755); err != nil {
			return nil, fmt.Errorf("failed to create log completions folder: %w", err)
		}
	}

	// Initialize LLM instance
	if err := llm.initLLMInstance(); err != nil {
		return nil, fmt.Errorf("failed to initialize LLM instance: %w", err)
	}

	// Log capabilities
	if llm.VisionIsActive() {
		core.OpenHandsLogger.Debug("LLM: model has vision enabled", nil)
	}
	if llm.IsCachingPromptActive() {
		core.OpenHandsLogger.Debug("LLM: caching prompt enabled", nil)
	}
	if llm.IsFunctionCallingActive() {
		core.OpenHandsLogger.Debug("LLM: model supports function calling", nil)
	}

	return llm, nil
}

// initLLMInstance initializes the underlying LLM instance
func (l *LLM) initLLMInstance() error {
	// Handle OpenHands provider - rewrite to litellm_proxy
	model := l.Config.Model
	baseURL := l.Config.BaseURL

	if strings.HasPrefix(model, "openhands/") {
		modelName := strings.TrimPrefix(model, "openhands/")
		model = fmt.Sprintf("litellm_proxy/%s", modelName)
		baseURL = stringPtr("https://llm-proxy.app.all-hands.dev/")
		core.OpenHandsLogger.Debug(fmt.Sprintf("Rewrote openhands/%s to %s with base URL %s", modelName, model, *baseURL), nil)
	}

	// Create LLM instance based on model type
	var err error
	if strings.Contains(model, "gpt") || strings.Contains(model, "openai") || strings.HasPrefix(model, "litellm_proxy/") {
		// Use OpenAI-compatible LLM
		opts := []openai.Option{
			openai.WithModel(model),
		}

		if l.Config.APIKey != nil {
			opts = append(opts, openai.WithToken(*l.Config.APIKey))
		}

		if baseURL != nil {
			opts = append(opts, openai.WithBaseURL(*baseURL))
		}

		l.llmInstance, err = openai.New(opts...)
	} else {
		// For other models, default to OpenAI-compatible
		opts := []openai.Option{
			openai.WithModel(model),
		}

		if l.Config.APIKey != nil {
			opts = append(opts, openai.WithToken(*l.Config.APIKey))
		}

		if baseURL != nil {
			opts = append(opts, openai.WithBaseURL(*baseURL))
		}

		l.llmInstance, err = openai.New(opts...)
	}

	return err
}

// initModelInfo initializes model information
func (l *LLM) initModelInfo() error {
	if l.TriedModelInfo {
		return nil
	}
	l.TriedModelInfo = true

	// For now, we'll set some basic model info
	// In a full implementation, this would query the model provider for capabilities
	l.ModelInfo = &ModelInfo{}

	// Set max_output_tokens from model info if not explicitly set
	if l.Config.MaxOutputTokens == nil {
		// Special case for Claude 3.7 Sonnet models
		if strings.Contains(l.Config.Model, "claude-3-7-sonnet") || strings.Contains(l.Config.Model, "claude-3.7-sonnet") {
			l.Config.MaxOutputTokens = intPtr(64000)
		}
	}

	// Initialize function calling capability
	modelNameSupported := containsString(FunctionCallingSupportedModels, l.Config.Model) ||
		containsAny(l.Config.Model, FunctionCallingSupportedModels)

	// Handle native_tool_calling user-defined configuration
	if l.Config.NativeToolCalling == nil {
		l.FunctionCallingActive = modelNameSupported
	} else {
		l.FunctionCallingActive = *l.Config.NativeToolCalling
	}

	return nil
}

// VisionIsActive returns whether vision is active for this LLM
func (l *LLM) VisionIsActive() bool {
	if l.Config.DisableVision != nil && *l.Config.DisableVision {
		return false
	}
	return l.supportsVision()
}

// supportsVision checks if the model supports vision
func (l *LLM) supportsVision() bool {
	// Check model info first
	if l.ModelInfo != nil && l.ModelInfo.SupportsVision != nil {
		return *l.ModelInfo.SupportsVision
	}

	// Check known vision-capable models
	visionModels := []string{
		"gpt-4o",
		"gpt-4-vision",
		"claude-3",
		"gemini",
	}

	return containsAny(l.Config.Model, visionModels)
}

// IsCachingPromptActive returns whether prompt caching is active
func (l *LLM) IsCachingPromptActive() bool {
	return l.Config.CachingPrompt && containsString(CachePromptSupportedModels, l.Config.Model)
}

// IsFunctionCallingActive returns whether function calling is active
func (l *LLM) IsFunctionCallingActive() bool {
	return l.FunctionCallingActive
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}

func containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func containsAny(item string, slice []string) bool {
	for _, s := range slice {
		if strings.Contains(item, s) {
			return true
		}
	}
	return false
}

// GenerateContent generates content using the LLM
func (l *LLM) GenerateContent(ctx context.Context, messages []core.Message, options ...CallOption) (*ContentResponse, error) {
	// Convert messages to langchaingo format
	langchainMessages := l.convertMessagesToLangChain(messages)

	// Apply configuration to options
	callOptions := l.buildCallOptions(options...)

	// Log the prompt
	l.LogPrompt(l.messagesToMap(messages))

	// Record start time for latency measurement
	startTime := time.Now()

	// Create retry configuration
	retryConfig := RetryConfig{
		NumRetries:      l.Config.NumRetries,
		RetryMinWait:    l.Config.RetryMinWait,
		RetryMaxWait:    l.Config.RetryMaxWait,
		RetryMultiplier: l.Config.RetryMultiplier,
		RetryListener:   l.RetryListener,
	}

	// Execute with retry
	result, err := l.RetryWithBackoff(ctx, retryConfig, func() (interface{}, error) {
		return l.llmInstance.GenerateContent(ctx, langchainMessages, callOptions...)
	})

	if err != nil {
		return nil, err
	}

	response, ok := result.(*ContentResponse)
	if !ok {
		return nil, fmt.Errorf("unexpected response type: %T", result)
	}

	// Calculate and record latency
	latency := time.Since(startTime).Seconds()
	responseID := "unknown"
	l.Metrics.AddResponseLatency(latency, responseID)

	// Log the response
	l.LogResponse(l.responseToMap(response))

	// Post-process the response
	l.postCompletion(response)

	// Log completion if enabled
	if l.Config.LogCompletions {
		if err := l.logCompletion(messages, response, callOptions); err != nil {
			core.OpenHandsLogger.Warning("Failed to log completion", map[string]interface{}{
				"error": err.Error(),
			})
		}
	}

	return response, nil
}

// Call provides a simple text-to-text interface
func (l *LLM) Call(ctx context.Context, prompt string, options ...CallOption) (string, error) {
	message := core.NewMessage("user")
	message.AddTextContent(prompt)

	response, err := l.GenerateContent(ctx, []core.Message{*message}, options...)
	if err != nil {
		return "", err
	}

	if len(response.Choices) == 0 {
		return "", core.NewLLMNoResponseError("No response choices returned")
	}

	return response.Choices[0].Content, nil
}

// convertMessagesToLangChain converts core.Message to langchaingo MessageContent
func (l *LLM) convertMessagesToLangChain(messages []core.Message) []llms.MessageContent {
	var langchainMessages []llms.MessageContent

	for _, msg := range messages {
		// Set flags for serialization
		msg.CacheEnabled = l.IsCachingPromptActive()
		msg.VisionEnabled = l.VisionIsActive()
		msg.FunctionCallingEnabled = l.IsFunctionCallingActive()

		// Force string serializer for certain models
		if strings.Contains(l.Config.Model, "deepseek") {
			msg.ForceStringSerializer = true
		}

		// Convert role
		var role llms.ChatMessageType
		switch msg.Role {
		case "user":
			role = llms.ChatMessageTypeHuman
		case "assistant":
			role = llms.ChatMessageTypeAI
		case "system":
			role = llms.ChatMessageTypeSystem
		case "tool":
			role = llms.ChatMessageTypeTool
		default:
			role = llms.ChatMessageTypeGeneric
		}

		// Convert content parts
		var parts []llms.ContentPart
		for _, content := range msg.Content {
			switch c := content.(type) {
			case *core.TextContent:
				parts = append(parts, llms.TextContent{Text: c.Text})
			case *core.ImageContent:
				if l.VisionIsActive() {
					for _, url := range c.ImageURLs {
						parts = append(parts, llms.ImageURLContent{URL: url})
					}
				}
			}
		}

		langchainMessage := llms.MessageContent{
			Role:  role,
			Parts: parts,
		}

		langchainMessages = append(langchainMessages, langchainMessage)
	}

	return langchainMessages
}

// buildCallOptions builds call options from configuration
func (l *LLM) buildCallOptions(options ...CallOption) []CallOption {
	// Start with provided options
	allOptions := make([]CallOption, len(options))
	copy(allOptions, options)

	// Add configuration-based options
	allOptions = append(allOptions,
		llms.WithTemperature(l.Config.Temperature),
		llms.WithTopP(l.Config.TopP),
	)

	if l.Config.MaxOutputTokens != nil {
		allOptions = append(allOptions, llms.WithMaxTokens(*l.Config.MaxOutputTokens))
	}

	if l.Config.TopK != nil {
		allOptions = append(allOptions, llms.WithTopK(int(*l.Config.TopK)))
	}

	if l.Config.Seed != nil {
		allOptions = append(allOptions, llms.WithSeed(*l.Config.Seed))
	}

	// Add stop words for models that support them
	if !containsString(ModelsWithoutStopWords, l.Config.Model) {
		allOptions = append(allOptions, llms.WithStopWords(STOP_WORDS))
	}

	return allOptions
}

// messagesToMap converts messages to map format for logging
func (l *LLM) messagesToMap(messages []core.Message) []map[string]interface{} {
	var result []map[string]interface{}
	for _, msg := range messages {
		result = append(result, msg.ModelDump())
	}
	return result
}

// responseToMap converts response to map format for logging
func (l *LLM) responseToMap(response *ContentResponse) map[string]interface{} {
	result := map[string]interface{}{
		"choices": make([]map[string]interface{}, len(response.Choices)),
	}

	for i, choice := range response.Choices {
		choiceMap := map[string]interface{}{
			"content":     choice.Content,
			"stop_reason": choice.StopReason,
		}

		if choice.GenerationInfo != nil {
			choiceMap["generation_info"] = choice.GenerationInfo
		}

		if choice.FuncCall != nil {
			choiceMap["function_call"] = map[string]interface{}{
				"name":      choice.FuncCall.Name,
				"arguments": choice.FuncCall.Arguments,
			}
		}

		if len(choice.ToolCalls) > 0 {
			toolCalls := make([]map[string]interface{}, len(choice.ToolCalls))
			for j, toolCall := range choice.ToolCalls {
				toolCalls[j] = map[string]interface{}{
					"id":   toolCall.ID,
					"type": toolCall.Type,
				}
				if toolCall.FunctionCall != nil {
					toolCalls[j]["function"] = map[string]interface{}{
						"name":      toolCall.FunctionCall.Name,
						"arguments": toolCall.FunctionCall.Arguments,
					}
				}
			}
			choiceMap["tool_calls"] = toolCalls
		}

		result["choices"].([]map[string]interface{})[i] = choiceMap
	}

	return result
}

// postCompletion handles post-processing of the completion response
func (l *LLM) postCompletion(response *ContentResponse) {
	// Calculate cost (simplified - in real implementation would use actual cost calculation)
	cost := 0.0 // Placeholder

	// Add cost to metrics
	l.Metrics.AddCost(cost)

	// Log stats
	stats := fmt.Sprintf("Cost: %.2f USD | Accumulated Cost: %.2f USD\n", cost, l.Metrics.GetAccumulatedCost())

	// Add latency to stats if available
	latencies := l.Metrics.GetResponseLatencies()
	if len(latencies) > 0 {
		latestLatency := latencies[len(latencies)-1]
		stats += fmt.Sprintf("Response Latency: %.3f seconds\n", latestLatency.Latency)
	}

	// Log token usage (simplified)
	if len(response.Choices) > 0 {
		// In a real implementation, we would extract token usage from the response
		// For now, we'll use placeholder values
		promptTokens := 0
		completionTokens := 0

		if promptTokens > 0 {
			stats += fmt.Sprintf("Input tokens: %d", promptTokens)
		}

		if completionTokens > 0 {
			if promptTokens > 0 {
				stats += " | "
			}
			stats += fmt.Sprintf("Output tokens: %d\n", completionTokens)
		}

		// Record in metrics
		l.Metrics.AddTokenUsage(promptTokens, completionTokens, 0, 0, 0, "unknown")
	}

	// Log the stats
	if stats != "" {
		core.OpenHandsLogger.Debug(stats, nil)
	}
}

// logCompletion logs the completion to file if enabled
func (l *LLM) logCompletion(messages []core.Message, response *ContentResponse, options []CallOption) error {
	if !l.Config.LogCompletions || l.Config.LogCompletionsFolder == "" {
		return nil
	}

	// Create log entry
	logEntry := map[string]interface{}{
		"messages":  l.messagesToMap(messages),
		"response":  l.responseToMap(response),
		"options":   options,
		"timestamp": time.Now().Unix(),
		"model":     l.Config.Model,
	}

	// Create filename
	modelName := strings.ReplaceAll(l.Metrics.ModelName, "/", "__")
	filename := fmt.Sprintf("%s-%d.json", modelName, time.Now().UnixNano())
	filepath := filepath.Join(l.Config.LogCompletionsFolder, filename)

	// Write to file
	data, err := json.MarshalIndent(logEntry, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal log entry: %w", err)
	}

	return os.WriteFile(filepath, data, 0644)
}

// GetTokenCount returns the number of tokens in a list of messages
func (l *LLM) GetTokenCount(messages []core.Message) int {
	// Simplified token counting - in a real implementation, this would use
	// a proper tokenizer for the specific model
	totalChars := 0
	for _, msg := range messages {
		for _, content := range msg.Content {
			if textContent, ok := content.(*core.TextContent); ok {
				totalChars += len(textContent.Text)
			}
		}
	}

	// Rough approximation: 4 characters per token
	return totalChars / 4
}

// FormatMessagesForLLM formats messages for LLM consumption
func (l *LLM) FormatMessagesForLLM(messages []core.Message) []map[string]interface{} {
	var result []map[string]interface{}

	for _, message := range messages {
		// Set flags for serialization
		message.CacheEnabled = l.IsCachingPromptActive()
		message.VisionEnabled = l.VisionIsActive()
		message.FunctionCallingEnabled = l.IsFunctionCallingActive()

		if strings.Contains(l.Config.Model, "deepseek") {
			message.ForceStringSerializer = true
		}

		result = append(result, message.ModelDump())
	}

	return result
}

// String returns a string representation of the LLM
func (l *LLM) String() string {
	if l.Config.APIVersion != nil {
		return fmt.Sprintf("LLM(model=%s, api_version=%s, base_url=%v)", l.Config.Model, *l.Config.APIVersion, l.Config.BaseURL)
	} else if l.Config.BaseURL != nil {
		return fmt.Sprintf("LLM(model=%s, base_url=%s)", l.Config.Model, *l.Config.BaseURL)
	}
	return fmt.Sprintf("LLM(model=%s)", l.Config.Model)
}

// Note: CallOption helper functions are provided by langchaingo/llms package:
// - llms.WithTemperature(temperature float64)
// - llms.WithTopP(topP float64)
// - llms.WithTopK(topK int)
// - llms.WithMaxTokens(maxTokens int)
// - llms.WithStopWords(stopWords []string)
// - llms.WithSeed(seed int)
// - llms.WithTools(tools []llms.Tool)
