package llm

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"kawai/openhands/core"
	"kawai/openhands/core/config"
)

// Model constants
var (
	// CachePromptSupportedModels lists models that support prompt caching
	CachePromptSupportedModels = []string{
		"claude-3-7-sonnet-20250219",
		"claude-sonnet-3-7-latest",
		"claude-3.7-sonnet",
		"claude-3-5-sonnet-20241022",
		"claude-3-5-sonnet-20240620",
		"claude-3-5-haiku-20241022",
		"claude-3-haiku-20240307",
		"claude-3-opus-20240229",
		"claude-sonnet-4-20250514",
		"claude-sonnet-4",
		"claude-opus-4-20250514",
	}

	// FunctionCallingSupportedModels lists models that support function calling
	FunctionCallingSupportedModels = []string{
		"claude-3-7-sonnet-20250219",
		"claude-sonnet-3-7-latest",
		"claude-3-5-sonnet",
		"claude-3-5-sonnet-20240620",
		"claude-3-5-sonnet-20241022",
		"claude-3.5-haiku",
		"claude-3-5-haiku-20241022",
		"claude-sonnet-4-20250514",
		"claude-sonnet-4",
		"claude-opus-4-20250514",
		"gpt-4o-mini",
		"gpt-4o",
		"o1-2024-12-17",
		"o3-mini-2025-01-31",
		"o3-mini",
		"o3",
		"o3-2025-04-16",
		"o4-mini",
		"o4-mini-2025-04-16",
		"gemini-2.5-pro",
		"gpt-4.1",
		"kimi-k2-0711-preview",
	}

	// ReasoningEffortSupportedModels lists models that support reasoning effort
	ReasoningEffortSupportedModels = []string{
		"o1-2024-12-17",
		"o1",
		"o3",
		"o3-2025-04-16",
		"o3-mini-2025-01-31",
		"o3-mini",
		"o4-mini",
		"o4-mini-2025-04-16",
		"gemini-2.5-flash",
		"gemini-2.5-pro",
	}

	// ModelsWithoutStopWords lists models that don't support stop words
	ModelsWithoutStopWords = []string{
		"o1-mini",
		"o1-preview",
		"o1",
		"o1-2024-12-17",
		"xai/grok-4-0709",
	}
)

// ModelInfo represents information about a model
type ModelInfo struct {
	MaxInputTokens  *int  `json:"max_input_tokens,omitempty"`
	MaxOutputTokens *int  `json:"max_output_tokens,omitempty"`
	MaxTokens       *int  `json:"max_tokens,omitempty"`
	SupportsVision  *bool `json:"supports_vision,omitempty"`
}

// CallOption represents options for LLM calls
type CallOption func(*CallOptions)

// CallOptions holds options for LLM calls
type CallOptions struct {
	Temperature  float64
	TopP         float64
	TopK         *int
	MaxTokens    *int
	StopWords    []string
	Seed         *int
	Tools        []Tool
}

// ContentResponse represents a response from the LLM
type ContentResponse struct {
	Choices []Choice `json:"choices"`
}

// Choice represents a single choice in the response
type Choice struct {
	Content        string                 `json:"content"`
	StopReason     string                 `json:"stop_reason"`
	GenerationInfo map[string]interface{} `json:"generation_info,omitempty"`
	FuncCall       *FunctionCall          `json:"function_call,omitempty"`
	ToolCalls      []core.ToolCall        `json:"tool_calls,omitempty"`
}

// FunctionCall represents a function call
type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// LLMModel defines the interface for LLM implementations
type LLMModel interface {
	GenerateContent(ctx context.Context, messages []map[string]interface{}, options *CallOptions) (*ContentResponse, error)
}

// HTTPLLMClient implements LLMModel using HTTP requests
type HTTPLLMClient struct {
	Model   string
	APIKey  *string
	BaseURL *string
}

// GenerateContent implements the LLMModel interface
func (h *HTTPLLMClient) GenerateContent(ctx context.Context, messages []map[string]interface{}, options *CallOptions) (*ContentResponse, error) {
	// This is a simplified implementation
	// In a real implementation, this would make HTTP requests to the LLM API

	// For now, return a mock response
	return &ContentResponse{
		Choices: []Choice{
			{
				Content:    "This is a mock response from the LLM",
				StopReason: "stop",
			},
		},
	}, nil
}

// LLM represents a Language Model instance
type LLM struct {
	DebugMixin
	RetryMixin

	Config                   *config.LLMConfig
	Metrics                  *Metrics
	RetryListener            RetryListener
	ModelInfo                *ModelInfo
	CostMetricSupported      bool
	FunctionCallingActive    bool
	TriedModelInfo           bool

	// LLM implementation
	llmInstance              LLMModel
}

// NewLLM creates a new LLM instance
func NewLLM(cfg *config.LLMConfig, metrics *Metrics, retryListener RetryListener) (*LLM, error) {
	if cfg == nil {
		cfg = config.NewLLMConfig()
	}

	if metrics == nil {
		metrics = NewMetrics(cfg.Model)
	}

	llm := &LLM{
		Config:              cfg,
		Metrics:             metrics,
		RetryListener:       retryListener,
		CostMetricSupported: true,
		TriedModelInfo:      false,
	}

	// Initialize model info
	if err := llm.initModelInfo(); err != nil {
		core.OpenHandsLogger.Warning("Failed to initialize model info", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// Create log completions folder if needed
	if cfg.LogCompletions {
		if cfg.LogCompletionsFolder == "" {
			return nil, fmt.Errorf("log_completions_folder is required when log_completions is enabled")
		}
		if err := os.MkdirAll(cfg.LogCompletionsFolder, 0755); err != nil {
			return nil, fmt.Errorf("failed to create log completions folder: %w", err)
		}
	}

	// Initialize LLM instance
	if err := llm.initLLMInstance(); err != nil {
		return nil, fmt.Errorf("failed to initialize LLM instance: %w", err)
	}

	// Log capabilities
	if llm.VisionIsActive() {
		core.OpenHandsLogger.Debug("LLM: model has vision enabled", nil)
	}
	if llm.IsCachingPromptActive() {
		core.OpenHandsLogger.Debug("LLM: caching prompt enabled", nil)
	}
	if llm.IsFunctionCallingActive() {
		core.OpenHandsLogger.Debug("LLM: model supports function calling", nil)
	}

	return llm, nil
}

// initLLMInstance initializes the underlying LLM instance
func (l *LLM) initLLMInstance() error {
	// Handle OpenHands provider - rewrite to litellm_proxy
	model := l.Config.Model
	baseURL := l.Config.BaseURL

	if strings.HasPrefix(model, "openhands/") {
		modelName := strings.TrimPrefix(model, "openhands/")
		model = fmt.Sprintf("litellm_proxy/%s", modelName)
		baseURL = stringPtr("https://llm-proxy.app.all-hands.dev/")
		core.OpenHandsLogger.Debug(fmt.Sprintf("Rewrote openhands/%s to %s with base URL %s", modelName, model, *baseURL), nil)
	}

	// Create a simple HTTP-based LLM instance
	l.llmInstance = &HTTPLLMClient{
		Model:   model,
		APIKey:  l.Config.APIKey,
		BaseURL: baseURL,
	}

	return nil
}

// initModelInfo initializes model information
func (l *LLM) initModelInfo() error {
	if l.TriedModelInfo {
		return nil
	}
	l.TriedModelInfo = true

	// For now, we'll set some basic model info
	// In a full implementation, this would query the model provider for capabilities
	l.ModelInfo = &ModelInfo{}

	// Set max_output_tokens from model info if not explicitly set
	if l.Config.MaxOutputTokens == nil {
		// Special case for Claude 3.7 Sonnet models
		if strings.Contains(l.Config.Model, "claude-3-7-sonnet") || strings.Contains(l.Config.Model, "claude-3.7-sonnet") {
			l.Config.MaxOutputTokens = intPtr(64000)
		}
	}

	// Initialize function calling capability
	modelNameSupported := containsString(FunctionCallingSupportedModels, l.Config.Model) ||
		containsAny(l.Config.Model, FunctionCallingSupportedModels)

	// Handle native_tool_calling user-defined configuration
	if l.Config.NativeToolCalling == nil {
		l.FunctionCallingActive = modelNameSupported
	} else {
		l.FunctionCallingActive = *l.Config.NativeToolCalling
	}

	return nil
}

// VisionIsActive returns whether vision is active for this LLM
func (l *LLM) VisionIsActive() bool {
	if l.Config.DisableVision != nil && *l.Config.DisableVision {
		return false
	}
	return l.supportsVision()
}

// supportsVision checks if the model supports vision
func (l *LLM) supportsVision() bool {
	// Check model info first
	if l.ModelInfo != nil && l.ModelInfo.SupportsVision != nil {
		return *l.ModelInfo.SupportsVision
	}

	// Check known vision-capable models
	visionModels := []string{
		"gpt-4o",
		"gpt-4-vision",
		"claude-3",
		"gemini",
	}

	return containsAny(l.Config.Model, visionModels)
}

// IsCachingPromptActive returns whether prompt caching is active
func (l *LLM) IsCachingPromptActive() bool {
	return l.Config.CachingPrompt && containsString(CachePromptSupportedModels, l.Config.Model)
}

// IsFunctionCallingActive returns whether function calling is active
func (l *LLM) IsFunctionCallingActive() bool {
	return l.FunctionCallingActive
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}

func containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func containsAny(item string, slice []string) bool {
	for _, s := range slice {
		if strings.Contains(item, s) {
			return true
		}
	}
	return false
}

// GenerateContent generates content using the LLM
func (l *LLM) GenerateContent(ctx context.Context, messages []core.Message, options ...CallOption) (*ContentResponse, error) {
	// Convert messages to map format
	messagesMaps := l.messagesToMap(messages)

	// Apply configuration to options
	callOptions := l.buildCallOptions(options...)

	// Log the prompt
	l.LogPrompt(messagesMaps)

	// Record start time for latency measurement
	startTime := time.Now()

	// Create retry configuration
	retryConfig := RetryConfig{
		NumRetries:      l.Config.NumRetries,
		RetryMinWait:    l.Config.RetryMinWait,
		RetryMaxWait:    l.Config.RetryMaxWait,
		RetryMultiplier: l.Config.RetryMultiplier,
		RetryListener:   l.RetryListener,
	}

	// Execute with retry
	result, err := l.RetryWithBackoff(ctx, retryConfig, func() (interface{}, error) {
		return l.llmInstance.GenerateContent(ctx, messagesMaps, callOptions)
	})

	if err != nil {
		return nil, err
	}

	response, ok := result.(*ContentResponse)
	if !ok {
		return nil, fmt.Errorf("unexpected response type: %T", result)
	}

	// Calculate and record latency
	latency := time.Since(startTime).Seconds()
	responseID := "unknown"
	l.Metrics.AddResponseLatency(latency, responseID)

	// Log the response
	l.LogResponse(l.responseToMap(response))

	// Post-process the response
	l.postCompletion(response)

	// Log completion if enabled
	if l.Config.LogCompletions {
		if err := l.logCompletion(messages, response, callOptions); err != nil {
			core.OpenHandsLogger.Warning("Failed to log completion", map[string]interface{}{
				"error": err.Error(),
			})
		}
	}

	return response, nil
}

// Call provides a simple text-to-text interface
func (l *LLM) Call(ctx context.Context, prompt string, options ...CallOption) (string, error) {
	message := core.NewMessage("user")
	message.AddTextContent(prompt)

	response, err := l.GenerateContent(ctx, []core.Message{*message}, options...)
	if err != nil {
		return "", err
	}

	if len(response.Choices) == 0 {
		return "", core.NewLLMNoResponseError("No response choices returned")
	}

	return response.Choices[0].Content, nil
}

// buildCallOptions builds call options from configuration
func (l *LLM) buildCallOptions(options ...CallOption) *CallOptions {
	// Start with default options
	callOptions := &CallOptions{
		Temperature: l.Config.Temperature,
		TopP:        l.Config.TopP,
		StopWords:   STOP_WORDS,
	}

	if l.Config.MaxOutputTokens != nil {
		callOptions.MaxTokens = l.Config.MaxOutputTokens
	}

	if l.Config.TopK != nil {
		topK := int(*l.Config.TopK)
		callOptions.TopK = &topK
	}

	if l.Config.Seed != nil {
		callOptions.Seed = l.Config.Seed
	}

	// Don't add stop words for models that don't support them
	if containsString(ModelsWithoutStopWords, l.Config.Model) {
		callOptions.StopWords = nil
	}

	// Apply provided options
	for _, option := range options {
		option(callOptions)
	}

	return callOptions
}

// messagesToMap converts messages to map format for logging
func (l *LLM) messagesToMap(messages []core.Message) []map[string]interface{} {
	var result []map[string]interface{}
	for _, msg := range messages {
		result = append(result, msg.ModelDump())
	}
	return result
}

// responseToMap converts response to map format for logging
func (l *LLM) responseToMap(response *ContentResponse) map[string]interface{} {
	result := map[string]interface{}{
		"choices": make([]map[string]interface{}, len(response.Choices)),
	}

	for i, choice := range response.Choices {
		choiceMap := map[string]interface{}{
			"content":     choice.Content,
			"stop_reason": choice.StopReason,
		}

		if choice.GenerationInfo != nil {
			choiceMap["generation_info"] = choice.GenerationInfo
		}

		if choice.FuncCall != nil {
			choiceMap["function_call"] = map[string]interface{}{
				"name":      choice.FuncCall.Name,
				"arguments": choice.FuncCall.Arguments,
			}
		}

		if len(choice.ToolCalls) > 0 {
			toolCalls := make([]map[string]interface{}, len(choice.ToolCalls))
			for j, toolCall := range choice.ToolCalls {
				toolCalls[j] = map[string]interface{}{
					"id":   toolCall.ID,
					"type": toolCall.Type,
				}
				if toolCall.Function != nil {
					toolCalls[j]["function"] = map[string]interface{}{
						"name":      toolCall.Function["name"],
						"arguments": toolCall.Function["arguments"],
					}
				}
			}
			choiceMap["tool_calls"] = toolCalls
		}

		result["choices"].([]map[string]interface{})[i] = choiceMap
	}

	return result
}

// postCompletion handles post-processing of the completion response
func (l *LLM) postCompletion(response *ContentResponse) {
	// Calculate cost (simplified - in real implementation would use actual cost calculation)
	cost := 0.0 // Placeholder

	// Add cost to metrics
	l.Metrics.AddCost(cost)

	// Log stats
	stats := fmt.Sprintf("Cost: %.2f USD | Accumulated Cost: %.2f USD\n", cost, l.Metrics.GetAccumulatedCost())

	// Add latency to stats if available
	latencies := l.Metrics.GetResponseLatencies()
	if len(latencies) > 0 {
		latestLatency := latencies[len(latencies)-1]
		stats += fmt.Sprintf("Response Latency: %.3f seconds\n", latestLatency.Latency)
	}

	// Log token usage (simplified)
	if len(response.Choices) > 0 {
		// In a real implementation, we would extract token usage from the response
		// For now, we'll use placeholder values
		promptTokens := 0
		completionTokens := 0

		if promptTokens > 0 {
			stats += fmt.Sprintf("Input tokens: %d", promptTokens)
		}

		if completionTokens > 0 {
			if promptTokens > 0 {
				stats += " | "
			}
			stats += fmt.Sprintf("Output tokens: %d\n", completionTokens)
		}

		// Record in metrics
		l.Metrics.AddTokenUsage(promptTokens, completionTokens, 0, 0, 0, "unknown")
	}

	// Log the stats
	if stats != "" {
		core.OpenHandsLogger.Debug(stats, nil)
	}
}

// logCompletion logs the completion to file if enabled
func (l *LLM) logCompletion(messages []core.Message, response *ContentResponse, options *CallOptions) error {
	if !l.Config.LogCompletions || l.Config.LogCompletionsFolder == "" {
		return nil
	}

	// Create log entry
	logEntry := map[string]interface{}{
		"messages":  l.messagesToMap(messages),
		"response":  l.responseToMap(response),
		"options":   options,
		"timestamp": time.Now().Unix(),
		"model":     l.Config.Model,
	}

	// Create filename
	modelName := strings.ReplaceAll(l.Metrics.ModelName, "/", "__")
	filename := fmt.Sprintf("%s-%d.json", modelName, time.Now().UnixNano())
	filepath := filepath.Join(l.Config.LogCompletionsFolder, filename)

	// Write to file
	data, err := json.MarshalIndent(logEntry, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal log entry: %w", err)
	}

	return os.WriteFile(filepath, data, 0644)
}

// GetTokenCount returns the number of tokens in a list of messages
func (l *LLM) GetTokenCount(messages []core.Message) int {
	// Simplified token counting - in a real implementation, this would use
	// a proper tokenizer for the specific model
	totalChars := 0
	for _, msg := range messages {
		for _, content := range msg.Content {
			if textContent, ok := content.(*core.TextContent); ok {
				totalChars += len(textContent.Text)
			}
		}
	}

	// Rough approximation: 4 characters per token
	return totalChars / 4
}

// FormatMessagesForLLM formats messages for LLM consumption
func (l *LLM) FormatMessagesForLLM(messages []core.Message) []map[string]interface{} {
	var result []map[string]interface{}

	for _, message := range messages {
		// Set flags for serialization
		message.CacheEnabled = l.IsCachingPromptActive()
		message.VisionEnabled = l.VisionIsActive()
		message.FunctionCallingEnabled = l.IsFunctionCallingActive()

		if strings.Contains(l.Config.Model, "deepseek") {
			message.ForceStringSerializer = true
		}

		result = append(result, message.ModelDump())
	}

	return result
}

// String returns a string representation of the LLM
func (l *LLM) String() string {
	if l.Config.APIVersion != nil {
		return fmt.Sprintf("LLM(model=%s, api_version=%s, base_url=%v)", l.Config.Model, *l.Config.APIVersion, l.Config.BaseURL)
	} else if l.Config.BaseURL != nil {
		return fmt.Sprintf("LLM(model=%s, base_url=%s)", l.Config.Model, *l.Config.BaseURL)
	}
	return fmt.Sprintf("LLM(model=%s)", l.Config.Model)
}

// CallOption helper functions

// WithTemperature sets the temperature for the LLM call
func WithTemperature(temperature float64) CallOption {
	return func(opts *CallOptions) {
		opts.Temperature = temperature
	}
}

// WithTopP sets the top-p value for the LLM call
func WithTopP(topP float64) CallOption {
	return func(opts *CallOptions) {
		opts.TopP = topP
	}
}

// WithTopK sets the top-k value for the LLM call
func WithTopK(topK int) CallOption {
	return func(opts *CallOptions) {
		opts.TopK = &topK
	}
}

// WithMaxTokens sets the maximum number of tokens for the LLM call
func WithMaxTokens(maxTokens int) CallOption {
	return func(opts *CallOptions) {
		opts.MaxTokens = &maxTokens
	}
}

// WithStopWords sets the stop words for the LLM call
func WithStopWords(stopWords []string) CallOption {
	return func(opts *CallOptions) {
		opts.StopWords = stopWords
	}
}

// WithSeed sets the seed for the LLM call
func WithSeed(seed int) CallOption {
	return func(opts *CallOptions) {
		opts.Seed = &seed
	}
}

// WithTools sets the tools for the LLM call
func WithTools(tools []Tool) CallOption {
	return func(opts *CallOptions) {
		opts.Tools = tools
	}
}
