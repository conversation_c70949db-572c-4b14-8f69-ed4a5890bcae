package llm

import (
	"sync"
	"time"
)

// TokenUsage represents token usage information
type TokenUsage struct {
	PromptTokens     int    `json:"prompt_tokens"`
	CompletionTokens int    `json:"completion_tokens"`
	CacheReadTokens  int    `json:"cache_read_tokens"`
	CacheWriteTokens int    `json:"cache_write_tokens"`
	ContextWindow    int    `json:"context_window"`
	ResponseID       string `json:"response_id"`
	Timestamp        int64  `json:"timestamp"`
}

// ResponseLatency represents response latency information
type ResponseLatency struct {
	Latency    float64 `json:"latency"`
	ResponseID string  `json:"response_id"`
	Timestamp  int64   `json:"timestamp"`
}

// Metrics tracks LLM usage metrics
type Metrics struct {
	mu                 sync.RWMutex
	ModelName          string            `json:"model_name"`
	AccumulatedCost    float64           `json:"accumulated_cost"`
	TokenUsages        []TokenUsage      `json:"token_usages"`
	ResponseLatencies  []ResponseLatency `json:"response_latencies"`
}

// NewMetrics creates a new Metrics instance
func NewMetrics(modelName string) *Metrics {
	return &Metrics{
		ModelName:         modelName,
		AccumulatedCost:   0.0,
		TokenUsages:       make([]TokenUsage, 0),
		ResponseLatencies: make([]ResponseLatency, 0),
	}
}

// AddCost adds cost to the accumulated cost
func (m *Metrics) AddCost(cost float64) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.AccumulatedCost += cost
}

// GetAccumulatedCost returns the accumulated cost
func (m *Metrics) GetAccumulatedCost() float64 {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.AccumulatedCost
}

// AddTokenUsage adds token usage information
func (m *Metrics) AddTokenUsage(promptTokens, completionTokens, cacheReadTokens, cacheWriteTokens, contextWindow int, responseID string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	usage := TokenUsage{
		PromptTokens:     promptTokens,
		CompletionTokens: completionTokens,
		CacheReadTokens:  cacheReadTokens,
		CacheWriteTokens: cacheWriteTokens,
		ContextWindow:    contextWindow,
		ResponseID:       responseID,
		Timestamp:        time.Now().Unix(),
	}
	
	m.TokenUsages = append(m.TokenUsages, usage)
}

// AddResponseLatency adds response latency information
func (m *Metrics) AddResponseLatency(latency float64, responseID string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	responseLatency := ResponseLatency{
		Latency:    latency,
		ResponseID: responseID,
		Timestamp:  time.Now().Unix(),
	}
	
	m.ResponseLatencies = append(m.ResponseLatencies, responseLatency)
}

// GetTokenUsages returns a copy of token usages
func (m *Metrics) GetTokenUsages() []TokenUsage {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	usages := make([]TokenUsage, len(m.TokenUsages))
	copy(usages, m.TokenUsages)
	return usages
}

// GetResponseLatencies returns a copy of response latencies
func (m *Metrics) GetResponseLatencies() []ResponseLatency {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	latencies := make([]ResponseLatency, len(m.ResponseLatencies))
	copy(latencies, m.ResponseLatencies)
	return latencies
}

// GetTotalTokens returns the total number of tokens used
func (m *Metrics) GetTotalTokens() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	total := 0
	for _, usage := range m.TokenUsages {
		total += usage.PromptTokens + usage.CompletionTokens
	}
	return total
}

// GetTotalPromptTokens returns the total number of prompt tokens used
func (m *Metrics) GetTotalPromptTokens() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	total := 0
	for _, usage := range m.TokenUsages {
		total += usage.PromptTokens
	}
	return total
}

// GetTotalCompletionTokens returns the total number of completion tokens used
func (m *Metrics) GetTotalCompletionTokens() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	total := 0
	for _, usage := range m.TokenUsages {
		total += usage.CompletionTokens
	}
	return total
}

// GetAverageLatency returns the average response latency
func (m *Metrics) GetAverageLatency() float64 {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if len(m.ResponseLatencies) == 0 {
		return 0.0
	}
	
	total := 0.0
	for _, latency := range m.ResponseLatencies {
		total += latency.Latency
	}
	return total / float64(len(m.ResponseLatencies))
}

// Reset resets all metrics
func (m *Metrics) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.AccumulatedCost = 0.0
	m.TokenUsages = make([]TokenUsage, 0)
	m.ResponseLatencies = make([]ResponseLatency, 0)
}
