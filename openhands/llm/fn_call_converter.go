package llm

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"kawai/openhands/core"
)

// Tool names constants
const (
	BROWSER_TOOL_NAME           = "browser"
	EXECUTE_BASH_TOOL_NAME      = "execute_bash"
	FINISH_TOOL_NAME            = "finish"
	LLM_BASED_EDIT_TOOL_NAME    = "llm_based_edit"
	STR_REPLACE_EDITOR_TOOL_NAME = "str_replace_editor"
)

// Stop words for function calling
var STOP_WORDS = []string{
	"</function_calls>",
	"</invoke>",
	"</parameter>",
	"</parameters>",
}

// Tool represents a tool definition
type Tool struct {
	Type     string                 `json:"type"`
	Function map[string]interface{} `json:"function"`
}

// ConvertFnCallMessagesToNonFnCallMessages converts function calling messages to non-function calling format
func ConvertFnCallMessagesToNonFnCallMessages(
	messages []map[string]interface{},
	tools []Tool,
	addInContextLearningExample bool,
) ([]map[string]interface{}, error) {
	
	if len(tools) == 0 {
		return messages, nil
	}

	// Create tools description
	toolsDescription, err := createToolsDescription(tools)
	if err != nil {
		return nil, fmt.Errorf("failed to create tools description: %w", err)
	}

	// Create system message with tools
	systemMessage := createSystemMessageWithTools(toolsDescription, addInContextLearningExample)
	
	// Convert messages
	convertedMessages := []map[string]interface{}{systemMessage}
	
	for _, message := range messages {
		convertedMessage, err := convertSingleMessage(message)
		if err != nil {
			return nil, fmt.Errorf("failed to convert message: %w", err)
		}
		if convertedMessage != nil {
			convertedMessages = append(convertedMessages, convertedMessage)
		}
	}

	return convertedMessages, nil
}

// ConvertNonFnCallMessagesToFnCallMessages converts non-function calling messages back to function calling format
func ConvertNonFnCallMessagesToFnCallMessages(
	messages []map[string]interface{},
	tools []Tool,
) ([]map[string]interface{}, error) {
	
	if len(messages) == 0 {
		return messages, nil
	}

	// Get the last message (should be the assistant response)
	lastMessage := messages[len(messages)-1]
	
	// Check if it's an assistant message
	if role, ok := lastMessage["role"].(string); !ok || role != "assistant" {
		return messages, nil
	}

	// Extract function calls from the content
	content, ok := lastMessage["content"].(string)
	if !ok {
		return messages, nil
	}

	toolCalls, err := extractToolCallsFromContent(content)
	if err != nil {
		core.OpenHandsLogger.Warning("Failed to extract tool calls from content", map[string]interface{}{
			"error":   err.Error(),
			"content": content,
		})
		return messages, nil
	}

	// If no tool calls found, return original messages
	if len(toolCalls) == 0 {
		return messages, nil
	}

	// Create new assistant message with tool calls
	newLastMessage := map[string]interface{}{
		"role":       "assistant",
		"content":    "", // Empty content when using tool calls
		"tool_calls": toolCalls,
	}

	// Replace the last message
	newMessages := make([]map[string]interface{}, len(messages))
	copy(newMessages, messages)
	newMessages[len(newMessages)-1] = newLastMessage

	return newMessages, nil
}

// createToolsDescription creates a description of available tools
func createToolsDescription(tools []Tool) (string, error) {
	var descriptions []string
	
	for _, tool := range tools {
		if tool.Type != "function" {
			continue
		}
		
		function := tool.Function
		name, ok := function["name"].(string)
		if !ok {
			continue
		}
		
		description, _ := function["description"].(string)
		
		// Create parameter description
		paramDesc := ""
		if params, ok := function["parameters"].(map[string]interface{}); ok {
			if properties, ok := params["properties"].(map[string]interface{}); ok {
				var paramList []string
				for paramName, paramInfo := range properties {
					if paramMap, ok := paramInfo.(map[string]interface{}); ok {
						paramType, _ := paramMap["type"].(string)
						paramDescription, _ := paramMap["description"].(string)
						paramList = append(paramList, fmt.Sprintf("  - %s (%s): %s", paramName, paramType, paramDescription))
					}
				}
				if len(paramList) > 0 {
					paramDesc = "\n  Parameters:\n" + strings.Join(paramList, "\n")
				}
			}
		}
		
		toolDesc := fmt.Sprintf("- %s: %s%s", name, description, paramDesc)
		descriptions = append(descriptions, toolDesc)
	}
	
	return strings.Join(descriptions, "\n"), nil
}

// createSystemMessageWithTools creates a system message that includes tool descriptions
func createSystemMessageWithTools(toolsDescription string, addInContextLearningExample bool) map[string]interface{} {
	content := fmt.Sprintf(`You have access to the following tools:

%s

To use a tool, please use the following format:

<function_calls>
<invoke name="tool_name">
<parameter name="parameter_name">parameter_value</parameter>
</invoke>
</function_calls>

You can call multiple tools by using multiple <invoke> blocks within the <function_calls> tags.`, toolsDescription)

	if addInContextLearningExample {
		content += `

Example:
<function_calls>
<invoke name="execute_bash">
<parameter name="command">ls -la</parameter>
</invoke>
</function_calls>`
	}

	return map[string]interface{}{
		"role":    "system",
		"content": content,
	}
}

// convertSingleMessage converts a single message from function calling to non-function calling format
func convertSingleMessage(message map[string]interface{}) (map[string]interface{}, error) {
	role, ok := message["role"].(string)
	if !ok {
		return message, nil
	}

	switch role {
	case "tool":
		// Convert tool message to user message
		content, _ := message["content"].(string)
		toolCallID, _ := message["tool_call_id"].(string)
		
		return map[string]interface{}{
			"role":    "user",
			"content": fmt.Sprintf("Tool execution result (ID: %s):\n%s", toolCallID, content),
		}, nil
		
	case "assistant":
		// Check if message has tool calls
		if toolCalls, ok := message["tool_calls"].([]interface{}); ok && len(toolCalls) > 0 {
			// Convert tool calls to XML format
			content, err := convertToolCallsToXML(toolCalls)
			if err != nil {
				return nil, err
			}
			
			return map[string]interface{}{
				"role":    "assistant",
				"content": content,
			}, nil
		}
		
		// Regular assistant message, return as-is
		return message, nil
		
	default:
		// Return other messages as-is
		return message, nil
	}
}

// convertToolCallsToXML converts tool calls to XML format
func convertToolCallsToXML(toolCalls []interface{}) (string, error) {
	var xmlParts []string
	
	for _, toolCall := range toolCalls {
		toolCallMap, ok := toolCall.(map[string]interface{})
		if !ok {
			continue
		}
		
		function, ok := toolCallMap["function"].(map[string]interface{})
		if !ok {
			continue
		}
		
		name, ok := function["name"].(string)
		if !ok {
			continue
		}
		
		argumentsStr, ok := function["arguments"].(string)
		if !ok {
			continue
		}
		
		// Parse arguments JSON
		var arguments map[string]interface{}
		if err := json.Unmarshal([]byte(argumentsStr), &arguments); err != nil {
			return "", fmt.Errorf("failed to parse tool call arguments: %w", err)
		}
		
		// Create XML for this tool call
		xmlPart := fmt.Sprintf(`<invoke name="%s">`, name)
		for paramName, paramValue := range arguments {
			xmlPart += fmt.Sprintf(`
<parameter name="%s">%v</parameter>`, paramName, paramValue)
		}
		xmlPart += `
</invoke>`
		
		xmlParts = append(xmlParts, xmlPart)
	}
	
	if len(xmlParts) == 0 {
		return "", nil
	}
	
	return fmt.Sprintf("<function_calls>\n%s\n</function_calls>", strings.Join(xmlParts, "\n")), nil
}

// extractToolCallsFromContent extracts tool calls from XML content
func extractToolCallsFromContent(content string) ([]map[string]interface{}, error) {
	// Regular expression to match function calls
	functionCallsRegex := regexp.MustCompile(`<function_calls>(.*?)</function_calls>`)
	invokeRegex := regexp.MustCompile(`<invoke name="([^"]+)">(.*?)</invoke>`)
	parameterRegex := regexp.MustCompile(`<parameter name="([^"]+)">([^<]*)</parameter>`)
	
	matches := functionCallsRegex.FindAllStringSubmatch(content, -1)
	if len(matches) == 0 {
		return nil, nil
	}
	
	var toolCalls []map[string]interface{}
	
	for _, match := range matches {
		functionCallsContent := match[1]
		
		invokeMatches := invokeRegex.FindAllStringSubmatch(functionCallsContent, -1)
		for i, invokeMatch := range invokeMatches {
			toolName := invokeMatch[1]
			invokeContent := invokeMatch[2]
			
			// Extract parameters
			parameters := make(map[string]interface{})
			paramMatches := parameterRegex.FindAllStringSubmatch(invokeContent, -1)
			for _, paramMatch := range paramMatches {
				paramName := paramMatch[1]
				paramValue := strings.TrimSpace(paramMatch[2])
				parameters[paramName] = paramValue
			}
			
			// Convert parameters to JSON string
			argumentsBytes, err := json.Marshal(parameters)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal tool call arguments: %w", err)
			}
			
			toolCall := map[string]interface{}{
				"id":   fmt.Sprintf("call_%d", i),
				"type": "function",
				"function": map[string]interface{}{
					"name":      toolName,
					"arguments": string(argumentsBytes),
				},
			}
			
			toolCalls = append(toolCalls, toolCall)
		}
	}
	
	return toolCalls, nil
}
