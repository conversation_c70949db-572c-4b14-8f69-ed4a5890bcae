package llm

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	"kawai/openhands/core"
)

// RetryListener is a function called when a retry occurs
type RetryListener func(attempt int, maxAttempts int)

// RetryMixin provides retry functionality for LLM operations
type RetryMixin struct{}

// RetryConfig holds configuration for retry behavior
type RetryConfig struct {
	NumRetries      int
	RetryMinWait    int // seconds
	RetryMaxWait    int // seconds
	RetryMultiplier float64
	RetryListener   RetryListener
}

// DefaultRetryConfig returns a default retry configuration
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		NumRetries:      4,
		RetryMinWait:    5,
		RetryMaxWait:    30,
		RetryMultiplier: 2.0,
		RetryListener:   nil,
	}
}

// RetryableError represents an error that can be retried
type RetryableError interface {
	error
	IsRetryable() bool
}

// IsRetryableError checks if an error should be retried
func IsRetryableError(err error) bool {
	if retryable, ok := err.(RetryableError); ok {
		return retryable.IsRetryable()
	}
	
	// Check for specific error types that should be retried
	switch err.(type) {
	case *core.LLMNoResponseError:
		return true
	default:
		// Check error message for common retryable patterns
		errMsg := err.Error()
		retryablePatterns := []string{
			"rate limit",
			"timeout",
			"service unavailable",
			"internal server error",
			"connection reset",
			"connection refused",
			"temporary failure",
		}
		
		for _, pattern := range retryablePatterns {
			if contains(errMsg, pattern) {
				return true
			}
		}
	}
	
	return false
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    len(s) > len(substr) && 
		    (s[:len(substr)] == substr || 
		     s[len(s)-len(substr):] == substr || 
		     indexOf(s, substr) >= 0))
}

// indexOf returns the index of substr in s, or -1 if not found
func indexOf(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// RetryWithBackoff executes a function with exponential backoff retry logic
func (r *RetryMixin) RetryWithBackoff(
	ctx context.Context,
	config RetryConfig,
	operation func() (interface{}, error),
) (interface{}, error) {
	var lastErr error
	
	for attempt := 0; attempt <= config.NumRetries; attempt++ {
		// Call retry listener if provided
		if config.RetryListener != nil {
			config.RetryListener(attempt, config.NumRetries)
		}
		
		// Execute the operation
		result, err := operation()
		if err == nil {
			return result, nil
		}
		
		lastErr = err
		
		// If this is the last attempt, don't retry
		if attempt == config.NumRetries {
			break
		}
		
		// Check if the error is retryable
		if !IsRetryableError(err) {
			core.OpenHandsLogger.Debug("Error is not retryable, stopping retry attempts", map[string]interface{}{
				"error":   err.Error(),
				"attempt": attempt + 1,
			})
			break
		}
		
		// Calculate wait time with exponential backoff
		waitTime := r.calculateWaitTime(attempt, config)
		
		core.OpenHandsLogger.Info(fmt.Sprintf("Retrying operation after error (attempt %d/%d)", attempt+1, config.NumRetries+1), map[string]interface{}{
			"error":     err.Error(),
			"wait_time": waitTime,
		})
		
		// Wait before retrying
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(time.Duration(waitTime) * time.Second):
			// Continue to next attempt
		}
	}
	
	return nil, fmt.Errorf("operation failed after %d attempts: %w", config.NumRetries+1, lastErr)
}

// calculateWaitTime calculates the wait time for a given attempt using exponential backoff
func (r *RetryMixin) calculateWaitTime(attempt int, config RetryConfig) int {
	// Calculate exponential backoff: min_wait * (multiplier ^ attempt)
	waitTime := float64(config.RetryMinWait) * math.Pow(config.RetryMultiplier, float64(attempt))
	
	// Add jitter (random factor to avoid thundering herd)
	jitter := rand.Float64() * 0.1 * waitTime // 10% jitter
	waitTime += jitter
	
	// Cap at max wait time
	if waitTime > float64(config.RetryMaxWait) {
		waitTime = float64(config.RetryMaxWait)
	}
	
	return int(waitTime)
}

// CreateRetryDecorator creates a retry decorator function
func (r *RetryMixin) CreateRetryDecorator(config RetryConfig) func(func() (interface{}, error)) func() (interface{}, error) {
	return func(operation func() (interface{}, error)) func() (interface{}, error) {
		return func() (interface{}, error) {
			ctx := context.Background() // Use background context for decorator
			return r.RetryWithBackoff(ctx, config, operation)
		}
	}
}
