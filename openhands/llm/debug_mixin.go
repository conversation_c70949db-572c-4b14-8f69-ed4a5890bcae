package llm

import (
	"encoding/json"
	"fmt"
	"strings"

	"kawai/openhands/core"
)

// DebugMixin provides debug functionality for LLM operations
type DebugMixin struct{}

// LogPrompt logs the LLM prompt
func (d *DebugMixin) LogPrompt(messages []map[string]interface{}) {
	// Create a formatted prompt string
	var promptParts []string
	for i, message := range messages {
		role := "unknown"
		if r, ok := message["role"].(string); ok {
			role = r
		}
		
		content := ""
		if c, ok := message["content"]; ok {
			switch v := c.(type) {
			case string:
				content = v
			case []interface{}:
				// Handle array of content parts
				var contentParts []string
				for _, part := range v {
					if partMap, ok := part.(map[string]interface{}); ok {
						if text, ok := partMap["text"].(string); ok {
							contentParts = append(contentParts, text)
						} else if partType, ok := partMap["type"].(string); ok {
							if partType == "image_url" {
								contentParts = append(contentParts, "[IMAGE]")
							}
						}
					}
				}
				content = strings.Join(contentParts, " ")
			default:
				if jsonBytes, err := json.Marshal(c); err == nil {
					content = string(jsonBytes)
				}
			}
		}
		
		promptParts = append(promptParts, fmt.Sprintf("[%d] %s: %s", i, role, content))
	}
	
	promptStr := strings.Join(promptParts, "\n")
	core.OpenHandsLogger.Debug("LLM Prompt", map[string]interface{}{
		"prompt": promptStr,
	})
}

// LogResponse logs the LLM response
func (d *DebugMixin) LogResponse(response map[string]interface{}) {
	// Extract key information from response
	responseInfo := make(map[string]interface{})
	
	if id, ok := response["id"]; ok {
		responseInfo["id"] = id
	}
	
	if model, ok := response["model"]; ok {
		responseInfo["model"] = model
	}
	
	if usage, ok := response["usage"].(map[string]interface{}); ok {
		responseInfo["usage"] = usage
	}
	
	// Extract choices
	if choices, ok := response["choices"].([]interface{}); ok {
		var choicesSummary []map[string]interface{}
		for i, choice := range choices {
			if choiceMap, ok := choice.(map[string]interface{}); ok {
				choiceSummary := map[string]interface{}{
					"index": i,
				}
				
				if message, ok := choiceMap["message"].(map[string]interface{}); ok {
					if role, ok := message["role"]; ok {
						choiceSummary["role"] = role
					}
					
					if content, ok := message["content"]; ok {
						// Truncate long content for logging
						contentStr := fmt.Sprintf("%v", content)
						if len(contentStr) > 500 {
							contentStr = contentStr[:500] + "..."
						}
						choiceSummary["content"] = contentStr
					}
					
					if toolCalls, ok := message["tool_calls"]; ok {
						choiceSummary["tool_calls"] = toolCalls
					}
				}
				
				if finishReason, ok := choiceMap["finish_reason"]; ok {
					choiceSummary["finish_reason"] = finishReason
				}
				
				choicesSummary = append(choicesSummary, choiceSummary)
			}
		}
		responseInfo["choices"] = choicesSummary
	}
	
	core.OpenHandsLogger.Debug("LLM Response", responseInfo)
}

// LogError logs an error that occurred during LLM operations
func (d *DebugMixin) LogError(err error, context string) {
	core.OpenHandsLogger.Error(fmt.Sprintf("LLM Error in %s", context), map[string]interface{}{
		"error": err.Error(),
	})
}

// LogWarning logs a warning during LLM operations
func (d *DebugMixin) LogWarning(message string, extra map[string]interface{}) {
	if extra == nil {
		extra = make(map[string]interface{})
	}
	extra["message"] = message
	core.OpenHandsLogger.Warning("LLM Warning", extra)
}

// LogInfo logs informational messages during LLM operations
func (d *DebugMixin) LogInfo(message string, extra map[string]interface{}) {
	if extra == nil {
		extra = make(map[string]interface{})
	}
	extra["message"] = message
	core.OpenHandsLogger.Info("LLM Info", extra)
}
