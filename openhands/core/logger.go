package core

import (
	"log"
	"os"
)

// Logger represents the OpenHands logger
type Logger struct {
	*log.Logger
}

// OpenHandsLogger is the global logger instance
var OpenHandsLogger *Logger

func init() {
	OpenHandsLogger = &Logger{
		Logger: log.New(os.Stdout, "[OpenHands] ", log.LstdFlags|log.Lshortfile),
	}
}

// Info logs an info message
func (l *Logger) Info(msg string, extra ...interface{}) {
	l.Printf("INFO: %s %v", msg, extra)
}

// Warning logs a warning message
func (l *Logger) Warning(msg string, extra ...interface{}) {
	l.Printf("WARNING: %s %v", msg, extra)
}

// Error logs an error message
func (l *Logger) Error(msg string, extra ...interface{}) {
	l.Printf("ERROR: %s %v", msg, extra)
}

// Debug logs a debug message
func (l *Logger) Debug(msg string, extra ...interface{}) {
	l.Printf("DEBUG: %s %v", msg, extra)
}
