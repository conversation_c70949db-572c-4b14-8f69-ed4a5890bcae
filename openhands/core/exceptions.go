package core

// LLMNoResponseError is raised when LLM did not return a response
type LLMNoResponseError struct {
	Message string
}

func (e LLMNoResponseError) Error() string {
	if e.Message == "" {
		return "LLM did not return a response. This is only seen in Gemini models so far."
	}
	return e.Message
}

// NewLLMNoResponseError creates a new LLMNoResponseError
func NewLLMNoResponseError(message string) *LLMNoResponseError {
	return &LLMNoResponseError{Message: message}
}

// LLMMalformedActionError is raised when LLM returns malformed response
type LLMMalformedActionError struct {
	Message string
}

func (e LLMMalformedActionError) Error() string {
	if e.Message == "" {
		return "Malformed response"
	}
	return e.Message
}

// NewLLMMalformedActionError creates a new LLMMalformedActionError
func NewLLMMalformedActionError(message string) *LLMMalformedActionError {
	return &LLMMalformedActionError{Message: message}
}

// LLMNoActionError is raised when agent did not return an action
type LLMNoActionError struct {
	Message string
}

func (e LLMNoActionError) Error() string {
	if e.Message == "" {
		return "Agent must return an action"
	}
	return e.Message
}

// NewLLMNoActionError creates a new LLMNoActionError
func NewLLMNoActionError(message string) *LLMNoActionError {
	return &LLMNoActionError{Message: message}
}

// LLMResponseError is raised when failed to retrieve action from LLM response
type LLMResponseError struct {
	Message string
}

func (e LLMResponseError) Error() string {
	if e.Message == "" {
		return "Failed to retrieve action from LLM response"
	}
	return e.Message
}

// NewLLMResponseError creates a new LLMResponseError
func NewLLMResponseError(message string) *LLMResponseError {
	return &LLMResponseError{Message: message}
}

// LLMContextWindowExceedError is raised when conversation history is too long
type LLMContextWindowExceedError struct {
	Message string
}

func (e LLMContextWindowExceedError) Error() string {
	if e.Message == "" {
		return "Conversation history longer than LLM context window limit. Consider turning on enable_history_truncation config to avoid this error"
	}
	return e.Message
}

// NewLLMContextWindowExceedError creates a new LLMContextWindowExceedError
func NewLLMContextWindowExceedError(message string) *LLMContextWindowExceedError {
	return &LLMContextWindowExceedError{Message: message}
}

// FunctionCallConversionError is raised when function call conversion fails
type FunctionCallConversionError struct {
	Message string
}

func (e FunctionCallConversionError) Error() string {
	if e.Message == "" {
		return "Function call conversion error"
	}
	return e.Message
}

// NewFunctionCallConversionError creates a new FunctionCallConversionError
func NewFunctionCallConversionError(message string) *FunctionCallConversionError {
	return &FunctionCallConversionError{Message: message}
}

// FunctionCallValidationError is raised when function call validation fails
type FunctionCallValidationError struct {
	Message string
}

func (e FunctionCallValidationError) Error() string {
	if e.Message == "" {
		return "Function call validation error"
	}
	return e.Message
}

// NewFunctionCallValidationError creates a new FunctionCallValidationError
func NewFunctionCallValidationError(message string) *FunctionCallValidationError {
	return &FunctionCallValidationError{Message: message}
}
