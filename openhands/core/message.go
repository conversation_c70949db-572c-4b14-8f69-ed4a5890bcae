package core

import (
	"encoding/json"
	"fmt"
)

// ContentType represents the type of content
type ContentType string

const (
	// TEXT represents text content
	TEXT ContentType = "text"
	// IMAGE_URL represents image URL content
	IMAGE_URL ContentType = "image_url"
)

// Content represents base content interface
type Content interface {
	GetType() string
	GetCachePrompt() bool
	SerializeModel() interface{}
}

// BaseContent provides common fields for content types
type BaseContent struct {
	Type        string `json:"type"`
	CachePrompt bool   `json:"cache_prompt,omitempty"`
}

// GetType returns the content type
func (c BaseContent) GetType() string {
	return c.Type
}

// GetCachePrompt returns whether cache prompt is enabled
func (c BaseContent) GetCachePrompt() bool {
	return c.CachePrompt
}

// TextContent represents text content
type TextContent struct {
	BaseContent
	Text string `json:"text"`
}

// NewTextContent creates a new TextContent
func NewTextContent(text string) *TextContent {
	return &TextContent{
		BaseContent: BaseContent{Type: string(TEXT)},
		Text:        text,
	}
}

// SerializeModel serializes the text content
func (tc *TextContent) SerializeModel() interface{} {
	data := map[string]interface{}{
		"type": tc.Type,
		"text": tc.Text,
	}
	if tc.CachePrompt {
		data["cache_control"] = map[string]string{"type": "ephemeral"}
	}
	return data
}

// ImageContent represents image content
type ImageContent struct {
	BaseContent
	ImageURLs []string `json:"image_urls"`
}

// NewImageContent creates a new ImageContent
func NewImageContent(imageURLs []string) *ImageContent {
	return &ImageContent{
		BaseContent: BaseContent{Type: string(IMAGE_URL)},
		ImageURLs:   imageURLs,
	}
}

// SerializeModel serializes the image content
func (ic *ImageContent) SerializeModel() interface{} {
	var images []map[string]interface{}
	for _, url := range ic.ImageURLs {
		images = append(images, map[string]interface{}{
			"type":      ic.Type,
			"image_url": map[string]string{"url": url},
		})
	}
	if ic.CachePrompt && len(images) > 0 {
		images[len(images)-1]["cache_control"] = map[string]string{"type": "ephemeral"}
	}
	return images
}

// ToolCall represents a tool call from LiteLLM
type ToolCall struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Function map[string]interface{} `json:"function"`
}

// Message represents a message in the conversation
type Message struct {
	Role                     string        `json:"role"`
	Content                  []Content     `json:"content"`
	CacheEnabled             bool          `json:"cache_enabled,omitempty"`
	VisionEnabled            bool          `json:"vision_enabled,omitempty"`
	FunctionCallingEnabled   bool          `json:"function_calling_enabled,omitempty"`
	ToolCalls                []ToolCall    `json:"tool_calls,omitempty"`
	ToolCallID               *string       `json:"tool_call_id,omitempty"`
	Name                     *string       `json:"name,omitempty"`
	ForceStringSerializer    bool          `json:"force_string_serializer,omitempty"`
}

// NewMessage creates a new Message
func NewMessage(role string) *Message {
	return &Message{
		Role:    role,
		Content: make([]Content, 0),
	}
}

// AddTextContent adds text content to the message
func (m *Message) AddTextContent(text string) {
	content := NewTextContent(text)
	m.Content = append(m.Content, content)
}

// AddImageContent adds image content to the message
func (m *Message) AddImageContent(imageURLs []string) {
	content := NewImageContent(imageURLs)
	m.Content = append(m.Content, content)
}

// ContainsImage returns true if the message contains image content
func (m *Message) ContainsImage() bool {
	for _, content := range m.Content {
		if _, ok := content.(*ImageContent); ok {
			return true
		}
	}
	return false
}

// ModelDump serializes the message for LLM consumption
func (m *Message) ModelDump() map[string]interface{} {
	var content []interface{}
	
	// Handle force string serializer
	if m.ForceStringSerializer {
		var textParts []string
		for _, item := range m.Content {
			if textContent, ok := item.(*TextContent); ok {
				textParts = append(textParts, textContent.Text)
			}
		}
		if len(textParts) > 0 {
			return map[string]interface{}{
				"content": textParts[0], // Use first text part as string
				"role":    m.Role,
			}
		}
	}

	// Normal serialization
	for _, item := range m.Content {
		serialized := item.SerializeModel()
		
		switch item.(type) {
		case *TextContent:
			content = append(content, serialized)
		case *ImageContent:
			if m.VisionEnabled {
				// ImageContent returns a slice, so we need to handle it properly
				if imageList, ok := serialized.([]map[string]interface{}); ok {
					for _, img := range imageList {
						content = append(content, img)
					}
				}
			}
		}
	}

	messageDict := map[string]interface{}{
		"content": content,
		"role":    m.Role,
	}

	// Add tool calls if present and function calling is enabled
	if m.FunctionCallingEnabled && len(m.ToolCalls) > 0 {
		messageDict["tool_calls"] = m.ToolCalls
	}

	// Add tool call ID if present
	if m.ToolCallID != nil {
		messageDict["tool_call_id"] = *m.ToolCallID
	}

	// Add name if present
	if m.Name != nil {
		messageDict["name"] = *m.Name
	}

	return messageDict
}

// String returns a string representation of the message
func (m *Message) String() string {
	data, err := json.Marshal(m.ModelDump())
	if err != nil {
		return fmt.Sprintf("Message{role: %s, error: %v}", m.Role, err)
	}
	return string(data)
}
