package config

import (
	"os"
	"path/filepath"
)

// LLMConfig represents the configuration for an LLM
type LLMConfig struct {
	Model                   string                   `json:"model"`
	APIKey                  *string                  `json:"api_key,omitempty"`
	BaseURL                 *string                  `json:"base_url,omitempty"`
	APIVersion              *string                  `json:"api_version,omitempty"`
	AWSAccessKeyID          *string                  `json:"aws_access_key_id,omitempty"`
	AWSSecretAccessKey      *string                  `json:"aws_secret_access_key,omitempty"`
	AWSRegionName           *string                  `json:"aws_region_name,omitempty"`
	OpenRouterSiteURL       string                   `json:"openrouter_site_url"`
	OpenRouterAppName       string                   `json:"openrouter_app_name"`
	NumRetries              int                      `json:"num_retries"`
	RetryMultiplier         float64                  `json:"retry_multiplier"`
	RetryMinWait            int                      `json:"retry_min_wait"`
	RetryMaxWait            int                      `json:"retry_max_wait"`
	Timeout                 *int                     `json:"timeout,omitempty"`
	MaxMessageChars         int                      `json:"max_message_chars"`
	Temperature             float64                  `json:"temperature"`
	TopP                    float64                  `json:"top_p"`
	TopK                    *float64                 `json:"top_k,omitempty"`
	CustomLLMProvider       *string                  `json:"custom_llm_provider,omitempty"`
	MaxInputTokens          *int                     `json:"max_input_tokens,omitempty"`
	MaxOutputTokens         *int                     `json:"max_output_tokens,omitempty"`
	InputCostPerToken       *float64                 `json:"input_cost_per_token,omitempty"`
	OutputCostPerToken      *float64                 `json:"output_cost_per_token,omitempty"`
	OllamaBaseURL           *string                  `json:"ollama_base_url,omitempty"`
	DropParams              bool                     `json:"drop_params"`
	ModifyParams            bool                     `json:"modify_params"`
	DisableVision           *bool                    `json:"disable_vision,omitempty"`
	CachingPrompt           bool                     `json:"caching_prompt"`
	LogCompletions          bool                     `json:"log_completions"`
	LogCompletionsFolder    string                   `json:"log_completions_folder"`
	CustomTokenizer         *string                  `json:"custom_tokenizer,omitempty"`
	NativeToolCalling       *bool                    `json:"native_tool_calling,omitempty"`
	ReasoningEffort         *string                  `json:"reasoning_effort,omitempty"`
	Seed                    *int                     `json:"seed,omitempty"`
	SafetySettings          []map[string]string      `json:"safety_settings,omitempty"`
}

// NewLLMConfig creates a new LLMConfig with default values
func NewLLMConfig() *LLMConfig {
	// Get log directory (simplified version)
	logDir := "logs"
	if homeDir, err := os.UserHomeDir(); err == nil {
		logDir = filepath.Join(homeDir, ".openhands", "logs")
	}

	return &LLMConfig{
		Model:                "claude-sonnet-4-20250514",
		OpenRouterSiteURL:    "https://docs.all-hands.dev/",
		OpenRouterAppName:    "OpenHands",
		NumRetries:           4,
		RetryMultiplier:      2.0,
		RetryMinWait:         5,
		RetryMaxWait:         30,
		MaxMessageChars:      30000,
		Temperature:          0.0,
		TopP:                 1.0,
		DropParams:           true,
		ModifyParams:         true,
		CachingPrompt:        true,
		LogCompletions:       false,
		LogCompletionsFolder: filepath.Join(logDir, "completions"),
		ReasoningEffort:      stringPtr("high"),
	}
}

// stringPtr returns a pointer to the given string
func stringPtr(s string) *string {
	return &s
}
