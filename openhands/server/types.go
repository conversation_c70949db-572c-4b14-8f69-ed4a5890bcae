package server

// AppM<PERSON> represents the application mode
type AppMode string

const (
	// OSS represents open source mode
	OSS AppMode = "oss"
	// SAAS represents software as a service mode
	SAAS AppMode = "saas"
)

// SessionMiddlewareInterface defines the interface for session middleware classes
type SessionMiddlewareInterface interface{}

// ServerConfigInterface defines the interface for server configuration
type ServerConfigInterface interface {
	// VerifyConfig verifies configuration settings
	VerifyConfig() error
	// GetConfig configures attributes for frontend
	GetConfig() (map[string]interface{}, error)
}

// MissingSettingsError is raised when settings are missing or not found
type MissingSettingsError struct {
	Message string
}

func (e MissingSettingsError) Error() string {
	return e.Message
}

// LLMAuthenticationError is raised when there is an issue with LLM authentication
type LLMAuthenticationError struct {
	Message string
}

func (e LLMAuthenticationError) Error() string {
	return e.Message
}
